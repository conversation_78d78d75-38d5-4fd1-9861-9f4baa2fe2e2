/* eslint-disable import/order */
require('dotenv').config();

const { NEW_RELIC_ENABLED } = require('./configs');

if (NEW_RELIC_ENABLED) {
  require('newrelic');
}

const logger = require('./utils/logger');

const Caching = require('./services/caching');
const { initMongoDB } = require('./models');
const {
  initGrowthBook,
  scheduleLoadFeaturesRealtime,
  getFeatureValue,
} = require('./services/growthbook');

const { RandomFactory } = require('./utils/random');

const { SERVICE } = require('./configs');
const { FEATURE_KEYS } = require('./constants/featureKeys');

// ADVISE: avoid using global (it hurts intellisense, code analysis tools, ...) if possible (should  import/export from a singleton module).
global.KAFKA_CONSUMER_GROUP_RANDOM_TTS = RandomFactory.getGuid();
global.VOICES = null;
global.LANGUAGES = null;
global.SAMPLE_SCRIPTS = null;
global.REQUEST_DIRECT = {};
global.AWS_ZONES_TTS_CACHING = {};
global.AWS_ZONES_TTS_STUDIO = {};

/**
 * AWS_S3_ACCESS[bucketName] = {s3AccessKeyId,s3SecretAccessKey};
 * With each bucket, we store the related credential.
 * When we call upload/download to S3, we have the bucketName, and credentials already
 */
global.AWS_S3_ACCESS = {};

global.logger = logger;
global.logLevel = () =>
  getFeatureValue(FEATURE_KEYS.LOG_LEVEL, { service: SERVICE });
global.BANNED_ACCOUNTS = [];

const { initApps } = require('./services/init/apps');
const { initIAM, scheduleTokenRefresh } = require('./services/init/iam');
const { initBlackWords } = require('./services/init/blackWords');
const { initLanguages } = require('./services/init/languages');
const { initSampleScripts } = require('./services/init/sampleScripts');
const { initVoices } = require('./services/init/voices');
const { initPackages } = require('./services/package');
const BannedAccountService = require('./services/BannedAccountService');
const {
  scheduleCheckDownstreamHealthy,
} = require('./services/healthCheck/downstream');
const {
  initTtsCachingAwsZone,
  initTtsStudioAwsZone,
  initS3AccessMapping,
} = require('./services/init/awsZone');

const { initHttpServer } = require('./express');
const { initWebSocketServer } = require('./services/ws');

// to be able to use async at top level, all app setup are in Immediately Invoked Function Expression (IIFE)
(async () => {
  /**
   * CriticalInitizations: crash the process if cannot initilize (Mongo, Cache, Growthbook, initIAM)
   * OneTimeInitializations: log error (send instant notification) and continue.
   * RecurringInitializations: periodically run process (scheduleTokenRefresh)
   */
  try {
    logger.info('\n\n= = = = = = CriticalInitializations: Start', {
      ctx: 'CriticalInitializations',
    });
    await Promise.all([
      //
      initGrowthBook(),
      Caching.init(),
      initMongoDB(),
      initIAM(),
    ]);
  } catch (error) {
    logger.error(error, { ctx: 'CriticalInitializations' });
    process.exit(1);
  }

  try {
    logger.info('\n\n= = = = = = OneTimeInitializations: Start', {
      ctx: 'OneTimeInitializations',
    });
    await Promise.all([
      initVoices(),
      initLanguages(),
      initBlackWords(),
      initApps(),
      initSampleScripts(),
      initPackages(),
      initTtsCachingAwsZone(),
      initTtsStudioAwsZone(),
      initS3AccessMapping(),
    ]);
  } catch (error) {
    // accept init failed
    logger.error(error, { ctx: 'OneTimeInitializations' });
  }

  try {
    logger.info('\n\n= = = = = = RecurringInitializations: Start', {
      ctx: 'RecurringInitializations',
    });
    await Promise.allSettled([
      //
      scheduleTokenRefresh(),
      scheduleCheckDownstreamHealthy(),
      scheduleLoadFeaturesRealtime(),
      BannedAccountService.scheduleSync(),
    ]);
  } catch (error) {
    // accept init failed
    logger.error(error, { ctx: 'RecurringInitializations' });
  }

  require('./services/kafka');

  const server = await initHttpServer();
  await initWebSocketServer(server);

  logger.info('\n\n= = = = = = Initializations: Finished', {
    ctx: 'Initializations',
  });
})();

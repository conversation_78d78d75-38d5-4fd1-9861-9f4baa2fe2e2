const daoUtils = require('./utils');
const logger = require('../utils/logger');
const Request = require('../models/request');
const {
  REQUEST_STATUS,
  REDIS_KEY_PREFIX,
  TTS_STATUS,
  REDIS_KEY_TTL,
  PACKAGE_TYPE,
} = require('../constants');
const { getSortQuery, getSelectQuery } = require('./utils/util');
const Caching = require('../services/caching');
const CustomError = require('../errors/CustomError');
const code = require('../errors/code');
const { getTtsFromRequestIdInRedis } = require('./tts');
const voiceCloningDao = require('./voiceCloning');

const createRequest = async (requestInfo) => {
  const request = await Request.create(requestInfo);
  return request;
};

const findRequestById = async (requestId) => {
  const [request] = await Request.aggregate([
    {
      $match: { _id: requestId },
    },
    {
      $addFields: { numberOfSentences: { $size: '$sentences' } },
    },
    {
      $lookup: {
        from: 'voices',
        localField: 'voiceCode',
        foreignField: 'code',
        as: 'voice',
      },
    },
    { $unwind: { path: '$voice', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'languages',
        localField: 'voice.languageCode',
        foreignField: 'code',
        as: 'voice.language',
      },
    },
    { $unwind: { path: '$voice.language', preserveNullAndEmptyArrays: true } },
    { $project: { voiceCode: 0 } },
  ]);

  return request;
};

const findRequestByIdInRedis = async (requestId) => {
  try {
    const requestKey = `${REDIS_KEY_PREFIX.REQUEST}_${requestId}`;
    const request = await Caching.RedisRepo.get(requestKey);
    if (!request) throw new CustomError(code.REQUEST_NOT_FOUND);

    const requestObj = JSON.parse(request);

    return requestObj;
  } catch (error) {
    logger.error(error, { ctx: 'RequestRedis', requestId });
    return {};
  }
};

const saveAudioLink = async (requestId, audioLink) => {
  const endedAt = new Date();
  const request = await Request.findByIdAndUpdate(
    requestId,
    { audioLink, status: REQUEST_STATUS.SUCCESS, endedAt },
    { new: true },
  ).lean();
  return request;
};

const saveAudioLinkInRedis = async (requestId, audioLink) => {
  const endedAt = new Date();
  const request = await updateRequestByIdInRedis(requestId, {
    audioLink,
    status: REQUEST_STATUS.SUCCESS,
    endedAt,
  });

  return request;
};

const findRequests = async ({
  search,
  query,
  offset,
  limit,
  fields,
  sort = ['createdAt_desc'],
}) => {
  if (search || query?.startDate || query?.endDate || query?.status)
    logger.info('Find requests', {
      ctx: 'SearchRequest',
      search,
      startDate: query?.startDate,
      endDate: query?.endDate,
      status: query?.status,
    });
  if (search) logger.info(search, { ctx: 'SearchTitleRequest' });
  // eslint-disable-next-line prefer-const
  let { documents: requests, total } = await daoUtils.find(Request, {
    search,
    searchFields: ['title'],
    dateField: 'createdAt',
    query,
    offset,
    limit,
    fields,
    sort,
  });
  const requestIds = requests.map((request) => request._id);
  const allFields = Object.keys(Request.schema.paths);

  const indexVoiceCode = allFields.indexOf('voiceCode');
  if (indexVoiceCode !== -1) {
    allFields[indexVoiceCode] = 'voice';
  }

  requests = await Request.aggregate([
    { $match: { _id: { $in: requestIds } } },
    {
      $lookup: {
        from: 'voices',
        localField: 'voiceCode',
        foreignField: 'code',
        as: 'voice',
      },
    },
    { $unwind: { path: '$voice', preserveNullAndEmptyArrays: true } },
    { $project: { voiceCode: 0 } },
    { $project: fields ? getSelectQuery(fields) : getSelectQuery(allFields) },
    { $sort: getSortQuery(sort) },
  ]);
  return { requests, total };
};

const getDetailSentencesByRequestId = async (requestId) => {
  const [request] = await Request.aggregate([
    { $match: { _id: requestId } },
    { $unwind: '$sentences' },
    {
      $lookup: {
        from: 'voices',
        localField: 'sentences.voiceCode',
        foreignField: 'code',
        as: 'sentences.voice',
      },
    },
    { $unwind: '$sentences.voice' },
    {
      $lookup: {
        from: 'languages',
        localField: 'sentences.voice.languageCode',
        foreignField: 'code',
        as: 'sentences.voice.language',
      },
    },
    { $unwind: '$sentences.voice.language' },
    {
      $group: {
        _id: '$_id',
        sentences: { $push: '$sentences' },
      },
    },
  ]);

  return request.sentences;
};

const updateRequestById = async (requestId, updateFields) => {
  const request = await Request.findByIdAndUpdate(requestId, updateFields, {
    new: true,
    omitUndefined: true,
  }).lean();
  return request;
};

// ADVISE: refactor to RequestCachingService
/** patch request cache */
const updateRequestByIdInRedis = async (requestId, updateFields) => {
  try {
    const requestKey = `${REDIS_KEY_PREFIX.REQUEST}_${requestId}`;
    const requestKeyTtl = REDIS_KEY_TTL.SYNTHESIS_REQUEST;

    let request = await Caching.RedisRepo.get(requestKey);
    if (!request) throw new CustomError(code.REQUEST_NOT_FOUND);

    request = JSON.parse(request);
    request = { ...request, ...updateFields };

    await Caching.RedisRepo.set(
      requestKey,
      JSON.stringify(request),
      requestKeyTtl,
    );

    return request;
  } catch (error) {
    logger.error(error, { ctx: 'RequestRedis', requestId });
    return {};
  }
};

const updateManyRequestsByIds = async (requestIds, updateFields) => {
  await Request.updateMany(
    {
      _id: { $in: requestIds },
    },
    updateFields,
  );
};

const hideRequestsByUser = async (userId, type) => {
  await Request.updateMany({ userId, type }, { hidden: true });
};

const checkRequestExists = async (condition) => {
  const isExists = await daoUtils.checkExists(Request, condition);
  return isExists;
};

const findRequest = async (condition, fields) => {
  const request = await daoUtils.findOne(Request, condition, fields);
  return request;
};

const updateTtsStatusInRequest = async ({ requestId, ttsId, status }) => {
  const request = await findRequestByIdInRedis(requestId);
  const { ttsIds = [] } = request || {};
  const ttsFailedIndex = ttsIds.findIndex((tts) => tts.ttsId === ttsId);

  if (ttsFailedIndex !== -1) {
    ttsIds[ttsFailedIndex].status = status;

    // ADVISE: most of the request are already in Cache, we only need to patch ttsIds into it
    const newRequest = { ...request, ttsIds };
    await updateRequestByIdInRedis(requestId, newRequest);
  }
};

const updateFinalRequestFromRedisToDB = async (requestId, cacheRequest) => {
  const {
    endedAt,
    progress,
    status,
    sentenceTokenizerDuration,
    t2aDuration,
    synthesisDuration,
    joinAudioDuration,
    audioLink,
    error,
    errorCode,
    firstStreamAudioDuration,
    ttsRequestId,
    sourceAudioLink,
  } = cacheRequest;

  await updateRequestById(requestId, {
    endedAt,
    progress,
    status,
    sentenceTokenizerDuration,
    t2aDuration,
    synthesisDuration,
    joinAudioDuration,
    audioLink,
    error,
    errorCode,
    ttsRequestId,
    firstStreamAudioDuration,
    sourceAudioLink,
  });
};

const checkDoneTts = async (requestId) => {
  const ttsList = await getTtsFromRequestIdInRedis(requestId);
  if (!ttsList || ttsList.length === 0) return true;
  const isNotDone = ttsList.some(
    (tts) => tts?.status === TTS_STATUS.IN_PROGRESS,
  );

  return !isNotDone;
};

const updateTtsInRequestInRedis = async ({ requestId, ttsId, status }) => {
  const request = await findRequestByIdInRedis(requestId);
  const { ttsIds = [] } = request || {};
  const ttsIndex = ttsIds.findIndex((tts) => tts.ttsId === ttsId);
  if (ttsIndex < 0) return;
  ttsIds[ttsIndex].status = status;

  const newRequest = { ...request, ttsIds };
  await updateRequestByIdInRedis(requestId, newRequest);
};

const increaseDownloadCount = async (requestId) => {
  await Request.findByIdAndUpdate(requestId, {
    $inc: { downloadCount: 1 },
  });
};

const updateDownloadStats = async (requestId, firstDownloadDuration) => {
  await Request.findByIdAndUpdate(requestId, {
    $inc: { downloadCount: 1 },
    firstDownloadDuration,
  });
};

const hideRequestsByProjectIds = async ({ projectIds, userId }) => {
  await Request.updateMany(
    { userId, projectId: { $in: projectIds } },
    { hidden: true },
  );
};

const hideAllRequestsInProjectByUser = async (userId) => {
  await Request.updateMany(
    { userId, type: PACKAGE_TYPE.DUBBING, projectId: { $exist: true } },
    { hidden: true },
  );
};

const deleteDatasenses = async (requestId) => {
  await Request.findByIdAndUpdate(requestId, { $unset: { datasenses: '' } });
};

const assignVoiceToRequest = async (request) => {
  const voiceInGlobal = VOICES.find(
    (voice) => voice.code === request.voiceCode,
  );
  if (voiceInGlobal) return { ...request, voice: voiceInGlobal };

  const clonedVoice = await voiceCloningDao.findVoiceCloningByCode(
    request.voiceCode,
  );
  return { ...request, voice: clonedVoice };
};

const findRequestsV2 = async ({
  search,
  query,
  offset,
  limit,
  fields,
  sort = ['createdAt_desc'],
}) => {
  // eslint-disable-next-line prefer-const
  let { documents: requests, total } = await daoUtils.find(Request, {
    search,
    searchFields: ['title'],
    dateField: 'createdAt',
    query,
    offset,
    limit,
    fields,
    sort,
  });

  // Assign voice to request
  const tempRequests = await Promise.all(requests.map(assignVoiceToRequest));

  const requestIds = tempRequests.map((request) => request._id);
  const mappedVoices = tempRequests.map((req) => req.voice);

  const allFields = Object.keys(Request.schema.paths);
  const indexVoiceCode = allFields.indexOf('voiceCode');
  if (indexVoiceCode !== -1) {
    allFields[indexVoiceCode] = 'voice';
  }

  requests = await Request.aggregate([
    { $match: { _id: { $in: requestIds } } },
    {
      $addFields: {
        voice: {
          $arrayElemAt: [mappedVoices, { $indexOfArray: [requestIds, '$_id'] }],
        },
      },
    },
    { $project: { voiceCode: 0 } },
    { $project: fields ? getSelectQuery(fields) : getSelectQuery(allFields) },
    { $sort: getSortQuery(sort) },
  ]);
  return { requests, total };
};

const countRequests = async (condition) => {
  const totalUsers = await Request.countDocuments(condition);
  return totalUsers;
};

const getRequests = async (query, { offset = 0, limit, sortField }) => {
  const users = await Request.find(query)
    .select({
      _id: 1,
      userId: 1,
      createdAt: 1,
      type: 1,
      voiceCode: 1,
      characters: 1,
      seconds: 1,
      sentences: 1,
      packageCode: 1,
    })
    .sort({ [sortField]: -1 })
    .skip(offset)
    .limit(limit);
  return users;
};

module.exports = {
  createRequest,
  findRequestById,
  findRequestByIdInRedis,
  saveAudioLink,
  saveAudioLinkInRedis,
  findRequests,
  getDetailSentencesByRequestId,
  updateRequestById,
  updateRequestByIdInRedis,
  updateManyRequestsByIds,
  hideRequestsByUser,
  checkRequestExists,
  findRequest,
  updateTtsStatusInRequest,
  updateFinalRequestFromRedisToDB,
  checkDoneTts,
  updateTtsInRequestInRedis,
  increaseDownloadCount,
  updateDownloadStats,
  hideRequestsByProjectIds,
  hideAllRequestsInProjectByUser,
  deleteDatasenses,
  findRequestsV2,
  assignVoiceToRequest,
  countRequests,
  getRequests,
};

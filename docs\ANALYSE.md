# Project Analysis and Architecture

- Backend for AIVoice Studio
- API for API Console (and API consumer)

### **Architecture Patterns**

- **Event-Driven Architecture**: Kafka-based message processing
- **API Gateway Pattern**: Multiple API versions (v1, v2, v3)
- **Repository Pattern**: DAO layer for data access
- **Shared library for common business**: sharable within microservices
  - vbee-node-shared-lib
  - vbee-tts-models

## **Core Features**

#### **Core Technologies**

- **Runtime**: Node.js 20.13 (Alpine Linux container), HTTP API and real-time Websocket connections
- **Framework**: Express.js with comprehensive middleware stack
- **Middleware Pipeline**: Express middleware for cross-cutting concerns
- **Database**: MongoDB with Mongoose ODM
- **Cache**: Redis for session management and caching
- **Message Queue**: Apache Kafka for asynchronous processing
- **Authentication**: JWT-based with IAM integration
- **Cloud Services**: AWS (S3, Lambda), Google Cloud Storage
- **Monitoring**: Sentry for error tracking, New Relic for APM
- **Multi-stage Docker Build**: Optimized for production

#### **Request Processing Pipeline**

1. **Request Validation**: Input sanitization and authentication
2. **Text Preprocessing**: Language detection, normalization
3. **Voice Selection**: Voice matching and permission checking
4. **Synthesis Queue**: Kafka-based job queuing
5. **Audio Generation**: AWS Lambda-based synthesis
6. **Post-processing**: Audio joining, background music mixing
7. **Storage & Delivery**: S3 storage with CDN delivery

#### **1. Text-to-Speech Synthesis**

- **Multi-voice Support**: 100+ AI voices with different languages, genders, and styles
- **Voice Cloning**: Custom voice creation and management
- **Real-time Processing**: WebSocket-based live synthesis
- **Batch Processing**: Asynchronous TTS processing via Kafka
- **Audio Formats**: MP3, WAV with configurable bitrates and sample rates

- **Background Music**: Audio mixing capabilities
- **Pronunciation Dictionary**: Custom word pronunciation
- **SSML Support**: Speech Synthesis Markup Language
- **Subtitle Generation**: SRT file creation
- **Voice Dubbing**: Video dubbing capabilities
- **Multi-language Support**: Vietnamese, English, and other languages

#### **2. API Versioning**

- **API v1**: Core TTS functionality with WebSocket support
- **API v2**: Enhanced features with improved voice management
- **API v3**: Legacy enterprise API compatibility
- **RESTful Design**: Standard HTTP methods with proper status codes

#### **3. User Management & Authentication**

- **JWT Authentication**: Secure token-based authentication
- **Role-Based Access Control**: Granular permissions system
- **Package Management**: Credit-based usage tracking
- **Rate Limiting**: Request throttling and quota management

### **Service Architecture**

#### **Key Local business Services**

- **Synthesis Service**: Core TTS processing logic
- **Voice Service**: Voice management and selection
- **User Service**: Account and credit management
- **Queue Service**: Kafka message handling
- **Cache Service**: Redis-based caching
- **Auth Service**: JWT token validation
- **Upload Service**: File handling for audio/images

#### **Internal Integrations** Modular service-oriented design

- **IAM Account**: user account
- **Payment Systems**: Credit processing
- **Notifier**: Slack, Google Chat notification

#### These are (un-organized) internal microservices which this service call

- VBEE_URL
- VN_URL
- CONSOLE_URL
- BACK_OFFICE_URL

- IAM_URL
- ACCOUNT_URL
- NOTIFICATION_URL
- UPLOAD_URL
- PAYMENT_URL

- TTS_GATE_URL
- AI_VOICE_CALLBACK_URL
- VBEE_DUBBING_URL
- STT_GATE_URL
- AIV_STT_CALLBACK_URL
- CLOUD_RUN_JOIN_URL
- SYNC_ENTERPRISE_SERVICE_URLS
- DATASENSES_URL

#### **External Integrations**

- **AWS Services**: S3, Lambda, CloudFront
- **Google Cloud**: Storage, reCAPTCHA Enterprise
- **Monitoring**: Sentry, New Relic, Prometheus metrics
- **Communication**: MoEngage analytics

## Detailed architectural diagram:

```mermaid
graph TB
    %% Client Layer
    subgraph "Client Applications"
        WEB[Web Application]
        MOBILE[Mobile Apps]
        API_CLIENT[API Clients]
        WS_CLIENT[WebSocket Clients]
    end

    %% Load Balancer & Gateway
    LB[Load Balancer]

    %% Main Application
    subgraph "VBEE TTS API Server"
        subgraph "Express.js Application"
            MW[Middleware Stack]
            ROUTES[Route Handlers]
            CTRL[Controllers]
        end

        subgraph "Core Services"
            SYNTH[Synthesis Service]
            VOICE[Voice Service]
            USER[User Service]
            AUTH[Auth Service]
            QUEUE[Queue Service]
            CACHE[Cache Service]
        end

        subgraph "Data Access Layer"
            DAO[DAO Layer]
            MODELS[Mongoose Models]
        end
    end

    %% WebSocket Server
    WSS[WebSocket Server<br/>Real-time TTS]

    %% Message Queue
    subgraph "Apache Kafka"
        KAFKA_PROD[Producer]
        KAFKA_TOPICS[Topics:<br/>- Synthesis Success/Failure<br/>- Sentence Tokenization<br/>- Audio Processing]
        KAFKA_CONS[Consumer Groups]
    end

    %% Databases
    subgraph "Data Storage"
        MONGO[(MongoDB<br/>Primary Database)]
        REDIS[(Redis<br/>Cache & Sessions)]
    end

    %% External Services
    subgraph "AWS Services"
        S3[S3 Storage<br/>Audio Files]
        LAMBDA[Lambda Functions<br/>TTS Processing]
        CF[CloudFront CDN]
    end

    subgraph "Google Cloud"
        GCS[Cloud Storage]
        RECAPTCHA[reCAPTCHA Enterprise]
    end

    subgraph "External APIs"
        IAM[IAM Service<br/>Authentication]
        PAYMENT[Payment Service]
        NOTIFICATION[Notification Service]
        MOENGAGE[MoEngage Analytics]
    end

    %% TTS Processing Pipeline
    subgraph "TTS Processing Pipeline"
        PREPROCESS[Text Preprocessing]
        TOKENIZER[Sentence Tokenizer]
        TTS_ENGINE[TTS Engine]
        AUDIO_JOIN[Audio Joiner]
        BG_MUSIC[Background Music Mixer]
    end

    %% Connections
    WEB --> LB
    MOBILE --> LB
    API_CLIENT --> LB
    WS_CLIENT --> WSS

    LB --> MW
    MW --> ROUTES
    ROUTES --> CTRL
    CTRL --> SYNTH
    CTRL --> VOICE
    CTRL --> USER
    CTRL --> AUTH

    SYNTH --> QUEUE
    QUEUE --> KAFKA_PROD
    KAFKA_PROD --> KAFKA_TOPICS
    KAFKA_TOPICS --> KAFKA_CONS
    KAFKA_CONS --> SYNTH

    USER --> DAO
    VOICE --> DAO
    AUTH --> DAO
    DAO --> MODELS
    MODELS --> MONGO

    CACHE --> REDIS
    SYNTH --> CACHE
    AUTH --> CACHE

    WSS --> SYNTH
    WSS --> AUTH
    WSS --> REDIS

    SYNTH --> PREPROCESS
    PREPROCESS --> TOKENIZER
    TOKENIZER --> TTS_ENGINE
    TTS_ENGINE --> AUDIO_JOIN
    AUDIO_JOIN --> BG_MUSIC

    TTS_ENGINE --> LAMBDA
    LAMBDA --> S3
    S3 --> CF

    SYNTH --> GCS
    AUTH --> RECAPTCHA

    MW --> SENTRY
    MW --> NEWRELIC
    QUEUE --> PROMETHEUS

    AUTH --> IAM
    USER --> PAYMENT
    SYNTH --> NOTIFICATION
    USER --> MOENGAGE

    %% Styling
    classDef clientStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef serverStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef dataStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef externalStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef processStyle fill:#fce4ec,stroke:#880e4f,stroke-width:2px

    class WEB,MOBILE,API_CLIENT,WS_CLIENT clientStyle
    class MW,ROUTES,CTRL,SYNTH,VOICE,USER,AUTH,QUEUE,CACHE,WSS serverStyle
    class MONGO,REDIS,S3,GCS dataStyle
    class IAM,PAYMENT,NOTIFICATION,MOENGAGE,SENTRY,NEWRELIC,PROMETHEUS,RECAPTCHA externalStyle
    class PREPROCESS,TOKENIZER,TTS_ENGINE,AUDIO_JOIN,BG_MUSIC processStyle
```

## data flow diagram:

```mermaid
sequenceDiagram
    participant Client
    participant API as Express API
    participant Auth as Auth Service
    participant Synth as Synthesis Service
    participant Queue as Kafka Queue
    participant Lambda as AWS Lambda
    participant S3 as AWS S3
    participant Redis
    participant MongoDB
    participant WS as WebSocket

    Note over Client,WS: TTS Request Processing Flow

    %% Authentication Flow
    Client->>API: POST /api/v1/tts (with JWT)
    API->>Auth: Verify Access Token
    Auth->>MongoDB: Validate User & App
    MongoDB-->>Auth: User Data
    Auth-->>API: Authentication Success

    %% Request Validation & Preprocessing
    API->>Synth: Handle Synthesis Request
    Synth->>MongoDB: Check Voice Permissions
    Synth->>MongoDB: Validate User Credits
    Synth->>Synth: Text Preprocessing & Validation
    Synth->>Redis: Create Request in Cache

    %% Queue Processing
    Synth->>Queue: Send to Kafka Topic
    Queue->>Lambda: Trigger TTS Processing

    %% TTS Processing Pipeline
    Lambda->>Lambda: Text Normalization
    Lambda->>Lambda: Sentence Tokenization
    Lambda->>Lambda: Voice Synthesis
    Lambda->>S3: Store Audio Files

    %% Success Response
    Lambda->>Queue: Synthesis Success Event
    Queue->>Synth: Process Success Message
    Synth->>MongoDB: Update Request Status
    Synth->>Redis: Update Progress Cache

    %% WebSocket Real-time Updates
    alt WebSocket Connection Active
        Synth->>WS: Send Progress Update
        WS->>Client: Real-time Status
    end

    %% Final Response
    Synth->>API: Processing Complete
    API->>Client: Return Audio URL

    Note over Client,WS: Error Handling Flow

    alt Processing Error
        Lambda->>Queue: Synthesis Failure Event
        Queue->>Synth: Process Error Message
        Synth->>MongoDB: Update Request with Error
        Synth->>API: Return Error Response
        API->>Client: Error Response
    end

    Note over Client,WS: Background Music & Post-Processing

    opt Background Music Requested
        Lambda->>Lambda: Audio Mixing
        Lambda->>S3: Store Mixed Audio
    end

    opt Subtitle Generation
        Lambda->>Lambda: Generate SRT File
        Lambda->>S3: Store Subtitle File
    end
```

## database relationships:

### **Models**

1. **User**: Account management, credits, packages, features
2. **Request**: TTS processing requests with status tracking
3. **Voice**: Voice catalog with metadata and capabilities
4. **Project**: User projects with blocks and audio management
5. **TTS**: Individual synthesis tasks with audio links
6. **VoiceCloning**: Custom voice management
7. **App**: API application management for enterprise clients

```mermaid
erDiagram
    User {
        string _id PK
        long remainingCharacters
        long bonusCharacters
        long lockCharacters
        string packageCode
        date packageStartDate
        date packageExpiryDate
        number maxLengthInputText
        number concurrentRequest
        number download
        number retentionPeriod
        array features
        number maxPreview
        number usedFreePreviews
        boolean isMigrated
        boolean isBlock
        object apiPackage
        object apiCharacters
        object studio
        object wallet
    }

    Request {
        string _id PK
        string title
        string text
        string subtitleLink
        string voiceCode FK
        number characters
        number credits
        number seconds
        array sentences
        string audioType
        object backgroundMusic
        number volume
        number speed
        string audioLink
        number bitrate
        string sampleRate
        object clientPause
        array pronunciationDict
        string userId FK
        string status
        date createdAt
        date processingAt
        date endedAt
        object usedCharacters
        object wallet
        string type
        string responseType
        string callbackUrl
        string projectId FK
        object timeProcess
        boolean isPriority
        boolean isRealTime
    }

    Voice {
        string code PK
        string ttsGateCode
        string name
        string image
        string gender
        string languageCode
        array secondaryLanguageCodes
        string type
        string provider
        string squareImage
        string roundImage
        string demo
        number rank
        array features
        array styles
        array sampleRates
        number defaultSampleRate
        string cachingFunction
        string synthesisFunction
        boolean active
        boolean global
        string level
        string version
        boolean beta
        boolean isSample
        object sample
        boolean hasDubbing
        date eolDate
        string canonicalVoiceCode FK
    }

    Project {
        string _id PK
        string userId FK
        string title
        string product
        array blocks
        boolean isDeleted
        number sampleRate
        number bitrate
        string audioType
    }

    TTS {
        string _id PK
        string requestId FK
        number index
        number subIndex
        string text
        string voiceCode FK
        string audioLink
        boolean silence
        array phrases
        string audioType
        string status
        string error
        number t2aDuration
        number synthesisDuration
        number start
        number end
    }

    VoiceCloning {
        string _id PK
        string userId FK
        string code
        string name
        string image
        string gender
        string locale
        string province
        string status
        string languageCode
        string type
        string provider
        array features
        array styles
        array sampleRates
        number defaultSampleRate
        string synthesisFunction
        boolean global
        string level
        string version
        boolean beta
        boolean isSample
        object sample
        boolean hasDubbing
        number retentionDays
        date discardAt
    }

    App {
        string _id PK
        string name
        string token
        date expiresAt
        string secretKey
        array members
        boolean active
        string server
    }

    ErrorReport {
        string _id PK
        object user
        string requestId FK
        string description
    }

    CallbackResult {
        string _id PK
        string requestId FK
        string statusCode
        string callbackUrl
        number index
        number retryCount
        date lastRetryAt
        string error
    }

    BackgroundMusic {
        string _id PK
        string name
        string link
        string image
        number rank
        boolean isActive
        string type
        number duration
    }

    Dictionary {
        string _id PK
        string userId FK
        string word
        string pronunciation
        string languageCode
        boolean isActive
    }

    %% Relationships
    User ||--o{ Request : "creates"
    User ||--o{ Project : "owns"
    User ||--o{ VoiceCloning : "creates"
    User ||--o{ Dictionary : "manages"

    Voice ||--o{ Request : "uses"
    Voice ||--o{ TTS : "synthesizes"
    Voice ||--|| Voice : "canonical_voice"

    Request ||--o{ TTS : "contains"
    Request ||--o{ ErrorReport : "reports"
    Request ||--o{ CallbackResult : "callbacks"
    Request }o--|| Project : "belongs_to"

    App ||--o{ Request : "generates"

    VoiceCloning }o--|| User : "owned_by"
```

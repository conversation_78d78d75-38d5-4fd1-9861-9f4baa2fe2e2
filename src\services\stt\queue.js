const Caching = require('../caching');
const { REDIS_KEY_PREFIX } = require('../../constants');
const logger = require('../../utils/logger');

const getNumOfPendAndInprReqKey = (userId) =>
  `${REDIS_KEY_PREFIX.API_STT_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`;

const getNumOfPendAndInprRequests = (userId) => {
  const numOfPendAndInprReqKey = getNumOfPendAndInprReqKey(userId);
  return Caching.RedisRepo.get(numOfPendAndInprReqKey);
};

const increaseNumOfPendAndInprRequests = async (userId) => {
  try {
    const numOfPendAndInprReqKey = getNumOfPendAndInprReqKey(userId);
    await Caching.GlobalCounter.increase(numOfPendAndInprReqKey);
  } catch (error) {
    logger.error('Increase number of pending and inprogress requests failed', {
      ctx: 'ProcessRequestByCcr',
      userId,
      stack: error.stack,
    });
  }
};

const decreaseNumOfPendAndInprRequests = async (userId) => {
  try {
    const numOfPendAndInprReqKey = getNumOfPendAndInprReqKey(userId);
    await Caching.GlobalCounter.decrease(numOfPendAndInprReqKey);
  } catch (error) {
    logger.error('Increase number of pending and inprogress requests failed', {
      ctx: 'ProcessRequestByCcr',
      userId,
      stack: error.stack,
    });
  }
};

module.exports = {
  getNumOfPendAndInprRequests,
  increaseNumOfPendAndInprRequests,
  decreaseNumOfPendAndInprRequests,
};

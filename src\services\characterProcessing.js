const { KAFKA_TOPIC, SYNC_CHARACTERS_EVENT } = require('../constants');
const { sendMessage } = require('./kafka/producer');

const refundCharacters = ({ userId, appId, requestId }) => {
  sendMessage(KAFKA_TOPIC.CHARACTERS_PROCESSING, {
    key: userId,
    value: {
      event: SYNC_CHARACTERS_EVENT.REFUND,
      userId,
      appId,
      requestId,
    },
  });
};

// ADVISE: should be in CreditService.spend()
const spendCharacters = ({
  userId,
  requestId,
  characters,
  blockedCredits,
  sentencesCreditsInfo,
}) => {
  sendMessage(KAFKA_TOPIC.CHARACTERS_PROCESSING, {
    key: userId,
    value: {
      event: SYNC_CHARACTERS_EVENT.SPEND,
      userId,
      requestId,
      characters,
      blockedCredits,
      sentencesCreditsInfo,
    },
  });
};

module.exports = { refundCharacters, spendCharacters };

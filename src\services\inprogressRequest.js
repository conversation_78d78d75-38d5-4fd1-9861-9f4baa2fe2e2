const inprogressRequestDao = require('../daos/inProgressRequest');
const requestDao = require('../daos/request');
const logger = require('../utils/logger');
const sleep = require('../utils/sleep');

const updateProcessingAt = async (requestId, processingAt) => {
  let maxRetry = 5;
  let inProgressRequest;
  await requestDao.updateRequestById(requestId, { processingAt });

  await requestDao.updateRequestByIdInRedis(requestId, { processingAt });

  do {
    await sleep(1000);
    inProgressRequest = await inprogressRequestDao.findInProgressRequestById(
      requestId,
    );
    maxRetry -= 1;
  } while (!inProgressRequest && maxRetry > 0);

  if (inProgressRequest)
    await inprogressRequestDao.updateProcessingAt(requestId, processingAt);
  else
    logger.warn(`Inprogress Request not found ${requestId}`, {
      ctx: 'UpdateProcessingAt',
    });
};

module.exports = { updateProcessingAt };

/* eslint-disable array-callback-return */
require('dotenv').config();
const snakeCaseKeys = require('snakecase-keys');

const logger = require('../../utils/logger');
const kafka = require('./kafka');
const { calculateMetricBasedOnKafkaTopic } = require('../metrics');

/** rescursively normalize message to be sent to Kafka */
const normalizeObject = (obj) => {
  if (typeof obj !== 'object') {
    return obj;
  }

  // typeof null = object
  if (obj === null) {
    return null;
  }

  // Check if obj is Mongoose Object
  if (obj._doc) {
    return normalizeObject(obj.toJSON());
  }

  // Check if obj is ObjectId
  if (obj._bsontype === 'ObjectID' || obj._bsontype === 'ObjectId') {
    return obj.toString();
  }

  Object.keys(obj).forEach((key) => {
    // eslint-disable-next-line no-param-reassign
    obj[key] = normalizeObject(obj[key]);
  });

  if (Array.isArray(obj)) return obj;
  if (Object.prototype.toString.call(obj) === '[object Date]') return obj;

  return obj;
};

const producer = kafka.producer();
producer.connect();

const sendMessage = async (topic, message) => {
  try {
    calculateMetricBasedOnKafkaTopic(topic);
    message = normalizeObject(message);
    message = snakeCaseKeys(message, { deep: true });
    message.value = JSON.stringify(message.value);

    const result = await producer.send({ topic, messages: [message] });
    logger.info(JSON.stringify({ topic, message, result }), {
      ctx: 'KafkaProduce',
    });
  } catch (error) {
    logger.error(error, { ctx: 'KafkaProduce' });
  }
};

const sendMessages = async (topic, messages = []) => {
  try {
    messages.forEach(() => calculateMetricBasedOnKafkaTopic(topic));
    messages = normalizeObject(messages);
    messages = snakeCaseKeys(messages, { deep: true });
    messages = messages.map((message) => ({
      ...message,
      value: JSON.stringify(message.value),
    }));

    const result = await producer.send({ topic, messages });
    logger.info(JSON.stringify({ topic, messages, result }), {
      ctx: 'KafkaProduce',
    });
  } catch (error) {
    logger.error(error, { ctx: 'KafkaProduce' });
  }
};

const run = async () => {
  try {
    await producer.connect();
  } catch (error) {
    logger.error(error, { ctx: 'KafkaProduce' });
  }
};

run().catch((e) => {
  logger.error(e, { ctx: 'KafkaProduce' });
});

const errorTypes = ['unhandledRejection', 'uncaughtException'];
const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2'];

errorTypes.map((type) => {
  process.on(type, async (e) => {
    try {
      logger.error(e, { ctx: 'ProcessError', type });
      await producer.disconnect();
      process.exit(0);
    } catch (_) {
      process.exit(1);
    }
  });
});

signalTraps.map((type) => {
  process.once(type, async () => {
    try {
      await producer.disconnect();
    } finally {
      process.kill(process.pid, type);
    }
  });
});

module.exports = { sendMessage, sendMessages };

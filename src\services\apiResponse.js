const https = require('https');
require('dotenv').config();
const { default: axios } = require('axios');
const snakecaseKeys = require('snakecase-keys');
const logger = require('../utils/logger');

const { VN_URL } = require('../configs');
const {
  RESPONSE_TYPE,
  OUTPUT_TYPE,
  API_RESPONSE_TYPE,
  V3_API_TYPE,
} = require('../constants');
const getErrorMessage = require('../errors/message');

const { createCallbackResult } = require('../daos/callbackResult');
const { updateRequestById } = require('../daos/request');

const { getAccessToken } = require('./iam');

const sendBinaryResponse = async ({ audioLink, audioType, res }) => {
  res.writeHead(200, {
    'Content-Type':
      audioType.indexOf('wav') !== -1 ? 'audio/x-wav' : 'audio/mpeg',
    'Content-disposition': `attachment; filename=${
      audioLink.split('/').slice(-1)[0]
    }`,
  });
  const response = await axios({
    url: audioLink,
    method: 'GET',
    responseType: 'stream',
  });
  response.data.pipe(res);
};

const sendCallBack = async ({ request, payload }) => {
  const { fromVn, requestId, sessionId } = request;
  let statusCode;
  let result;

  try {
    const callbackUrl = fromVn
      ? `${VN_URL}/api/v1/callback?callbackUrl=${encodeURIComponent(
          request.callbackUrl,
        )}`
      : request.callbackUrl;

    const httpsAgent = new https.Agent({ rejectUnauthorized: false });

    const response = await axios({
      method: 'POST',
      url: callbackUrl,
      data: payload,
      httpsAgent,
    });

    logger.info(JSON.stringify(response.data), {
      ctx: 'SendApiResponse',
      requestId,
      sessionId,
    });

    statusCode = response.status;
    result = response.data;
  } catch (err) {
    logger.error(err, { ctx: 'SendApiResponse', requestId, sessionId });

    statusCode = err.response?.status || 500;
    result = err.response?.data || err.response?.statusText || err.message;
  }

  return { statusCode, result };
};

const getTypeResponseData = (v3ApiType, responseType) => {
  if (!v3ApiType) return API_RESPONSE_TYPE.STUDIO_API;
  if (
    v3ApiType === V3_API_TYPE.ENTERPRISE &&
    responseType === RESPONSE_TYPE.INDIRECT
  )
    return API_RESPONSE_TYPE.V3_ENTERPRISE_INDIRECT;
  if (
    v3ApiType === V3_API_TYPE.ENTERPRISE &&
    responseType === RESPONSE_TYPE.DIRECT
  )
    return API_RESPONSE_TYPE.V3_ENTERPRISE_DIRECT;
  if (v3ApiType === V3_API_TYPE.ARTICLE) return API_RESPONSE_TYPE.V3_ARTICLE;
  if (v3ApiType === V3_API_TYPE.PERSONAL) return API_RESPONSE_TYPE.V3_PERSONAL;
  return '';
};

const updateTimeProcessRequest = async ({
  requestId,
  timeProcess,
  preSendResponseAt,
  startSendResponseAt,
}) => {
  const endSendResponse = new Date();
  const processDuration = endSendResponse - Date.parse(timeProcess.receivedAt);
  const sendResponseEndAt = new Date();
  timeProcess = {
    ...timeProcess,
    preSendResponseAt,
    startSendResponseAt,
    sendResponseEndAt,
    processDuration: Number(processDuration || 0),
  };
  updateRequestById(requestId, { timeProcess });
};

const sendApiResponse = async ({ request, res, errorCode, errorMessage }) => {
  const {
    requestId,
    isV3Version,
    outputType,
    audioType,
    responseType,
    v3ApiType,
    fromVn,
    timeProcess = {},
    preSendResponseAt,
    timestampWords,
  } = request;

  const startSendResponseAt = new Date();
  let payload = {
    errorCode,
    errorMessage: errorMessage || getErrorMessage(errorCode) || undefined,
    status: errorCode && errorMessage ? 0 : 1,
  };

  // Response Data with API V3: ENTERPRISE and ARTICLE
  let v3Payload = {
    error: errorCode && errorMessage ? 1 : 0,
    requestId,
    link: request.audioLink,
    errorMessage:
      errorMessage || errorCode ? getErrorMessage(errorCode) : 'Thành công',
  };

  // response data success
  if (!errorCode && !errorMessage) {
    const {
      app,
      text,
      characters,
      voiceCode,
      speed,
      sampleRate,
      bitrate,
      createdAt,
      status,
      callbackUrl,
    } = request;

    const accessToken = await getAccessToken();
    const audioLink = await require('./request').getAudioDownloadUrl({
      requestId,
      authorization: `Bearer ${accessToken}`,
    });
    const responseDataType = getTypeResponseData(v3ApiType, responseType);

    if (
      request.responseType === RESPONSE_TYPE.DIRECT &&
      outputType === OUTPUT_TYPE.BINARY &&
      !fromVn &&
      res
    ) {
      await sendBinaryResponse({ audioLink, audioType, res });
      updateTimeProcessRequest({
        requestId,
        timeProcess,
        preSendResponseAt,
        startSendResponseAt,
      });
      return;
    }

    switch (responseDataType) {
      case API_RESPONSE_TYPE.STUDIO_API:
        payload = {
          appId: app,
          requestId,
          characters,
          voiceCode,
          audioType,
          speedRate: speed,
          bitrate,
          sampleRate,
          createdAt,
          status,
          timestampWords,
          audioLink,
        };
        break;
      case API_RESPONSE_TYPE.V3_ENTERPRISE_INDIRECT:
        v3Payload = {
          statusCode: 0,
          message: 'Thành công',
          link: request.audioLink,
          request: {
            requestId,
            inputText: text,
            countText: characters,
            voice: voiceCode,
            urlCallbackApi: callbackUrl,
            createdAt,
          },
        };
        break;
      case API_RESPONSE_TYPE.V3_ENTERPRISE_DIRECT:
        v3Payload = {
          error: 0,
          msg: 'Thành công',
          requestId,
          message: '',
          link: audioLink,
        };
        break;
      case API_RESPONSE_TYPE.V3_PERSONAL:
        v3Payload = {
          statusCode: 0,
          message: 'Convert Success !',
          link: request.audioLink,
          request: {
            requestId,
            inputText: text,
            countText: characters,
            voice: voiceCode,
            urlCallbackApi: callbackUrl,
            createdAt: {
              date: createdAt,
              timezone: 'Asia/Ho_Chi_Minh',
            },
          },
        };
        break;
      default:
        break;
    }
  }

  let sendPayload = isV3Version || v3ApiType ? v3Payload : payload;
  sendPayload = snakecaseKeys(sendPayload, { deep: true });

  if (request.responseType === RESPONSE_TYPE.DIRECT && res) {
    res.send(sendPayload);
    delete global.REQUEST_DIRECT[requestId];
    updateTimeProcessRequest({
      requestId,
      timeProcess,
      preSendResponseAt,
      startSendResponseAt,
    });

    return;
  }

  const { statusCode, result } = await sendCallBack({
    request,
    payload: sendPayload,
  });
  createCallbackResult({
    requestId,
    callbackUrl: request.callbackUrl,
    payload: sendPayload,
    statusCode,
    result,
  });
};

module.exports = { sendApiResponse };

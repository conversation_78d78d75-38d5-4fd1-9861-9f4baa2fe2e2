const retry = require('retry');
const callApi = require('../utils/callApi');
const { TTS_GATE_URL, AI_VOICE_TOKEN } = require('../configs');
const logger = require('../utils/logger');

class TtsGateAdapter {
  static async execute({ apiEndpoint, data }) {
    const res = await callApi({
      headers: { authorization: `Bearer ${AI_VOICE_TOKEN}` },
      url: `${TTS_GATE_URL}/api/v1/${apiEndpoint}`,
      method: 'POST',
      data,
    });
    return res;
  }

  static async executeWithRetry({ apiEndpoint, data, requestId }) {
    return new Promise((resolve, reject) => {
      const operation = retry.operation({
        retries: 3, // Maximum 3 retries
        factor: 2, // Exponential backoff factor
        minTimeout: 2000, // Start with 2 second delay
        maxTimeout: 20000, // Max 20 seconds between retries
        randomize: true, // Add some randomization to prevent thundering herd
      });

      operation.attempt(async (currentAttempt) => {
        try {
          const result = await TtsGateAdapter.execute({
            apiEndpoint,
            data,
          });
          resolve(result);
        } catch (err) {
          if (operation.retry(err)) {
            logger.warn(
              `TTS Gate synthesis attempt ${currentAttempt} failed, retrying...`,
              { ctx: 'TtsGateAdapter.executeWithRetry', requestId },
            );
            return;
          }
          reject(operation.mainError());
        }
      });
    });
  }
}

module.exports = {
  TtsGateAdapter,
};

const logger = require('../utils/logger'); // require('');
const asyncMiddleware = require('./async');
const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');
const authService = require('../services/auth');
const authS2S = require('../services/authS2S');
const { TTS_CLIENT_ID } = require('../configs');

const auth = async (req, res, next) => {
  const { authorization } = req.headers;
  if (!authorization) throw new CustomError(errorCodes.UNAUTHORIZED);

  const [tokenType, accessToken] = authorization.split(' ');

  if (tokenType !== 'Bearer') throw new Error(errorCodes.UNAUTHORIZED);

  const data = await authService.verifyAccessToken(accessToken);
  const { sub: userId, email, name, phoneNumber, resourceAccess } = data;

  req.user = { userId, email, name, phoneNumber };
  req.roles = resourceAccess[TTS_CLIENT_ID]?.roles || [];

  return next();
};

/** Auth for PaygApi */
const authAPI = async (req, res, next) => {
  const receivedAt = new Date();
  const { authorization } = req.headers;
  const appId = req.headers['app-id'] || req.body.appId || req.query['app-id'];

  if (!appId) throw new CustomError(errorCodes.UNAUTHORIZED);
  if (!authorization) throw new CustomError(errorCodes.UNAUTHORIZED);

  const [tokenType, accessToken] = authorization.split(' ');

  if (tokenType !== 'Bearer') throw new Error(errorCodes.UNAUTHORIZED);

  const app = await authService.verifyAccessTokenAPI(appId, accessToken);
  req.apiApp = app;
  req.receivedAt = receivedAt;

  next();
};

const authApiV3 = async (req, res, next) => {
  logger.info(req.body, { ctx: 'V3Api' });
  const { appId, applicationId } = req.body;

  if (!appId && !applicationId) throw new CustomError(errorCodes.UNAUTHORIZED);

  const app = await authService.verifyKeyV3(appId || applicationId);
  req.apiApp = app;

  next();
};

const authService2Service = async (req, res, next) => {
  const { authorization } = req.headers;
  if (!authorization) throw new CustomError(errorCodes.UNAUTHORIZED);

  const [tokenType, accessToken] = authorization.split(' ');

  if (tokenType !== 'Bearer') throw new Error(errorCodes.UNAUTHORIZED);
  const { role } = req;
  const { roles, azp } = await authS2S.verifyAccessToken(accessToken, role);

  if (roles) req.roles = roles[azp];

  return next();
};

const hasRole = (role) => (req, res, next) => {
  const userRoles = req.roles;

  const hasPermission = userRoles.includes(role);
  if (!hasPermission) throw new CustomError(errorCodes.FORBIDDEN);

  return next();
};

const setRole = (role) => (req, res, next) => {
  req.role = role;

  return next();
};

module.exports = {
  auth: asyncMiddleware(auth),
  /** Auth for PaygApi */
  authAPI: asyncMiddleware(authAPI),
  authApiV3: asyncMiddleware(authApiV3),
  authS2S: asyncMiddleware(authService2Service),
  hasRole,
  setRole,
};

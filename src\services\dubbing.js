const moment = require('moment');
const {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} = require('@vbee-holding/vbee-node-shared-lib');

const callApi = require('../utils/callApi');
const { readFileFromLink } = require('../utils/file');
const logger = require('../utils/logger');
const CustomError = require('../errors/CustomError');
const code = require('../errors/code');
const { findUser } = require('../daos/user');
const { validateText } = require('./preprocessing');
const { getVoiceByCode } = require('./voice');
const userDao = require('../daos/user');
const requestDao = require('../daos/request');
const orderDao = require('../daos/order');

const {
  STUDIO_TTS_VERSION_DEFAULT,
  MULTI_ZONE,
  DEFAULT_AWS_REGION,
  DEFAULT_AUDIO_TYPE,
  VBEE_DUBBING_URL,
  DEFAULT_RETENTION_PERIOD,
} = require('../configs');
const {
  REQUEST_STATUS,
  REQUEST_TYPE,
  SERVICE_TYPE,
  REDIS_KEY_PREFIX,
  KAFKA_TOPIC,
  SYNC_SECONDS_EVENT,
  PACKAGE_CODE,
  PACKAGE_TYPE,
  RESOURCE_TYPE,
  PACKAGE_FEATURE,
} = require('../constants');
const { createRequest, createRequestInRedis } = require('./request');
const { pushRequestToQueue } = require('./queue');
const { getAwsZone } = require('../daos/awsZone');
const { spendCharacters } = require('./characterProcessing');
const { getTtsFromRequestIdInRedis } = require('../daos/tts');
const { sendMessage } = require('./kafka/producer');
const { getSRTDuration, parseSRT } = require('../utils/srt');
const {
  DUBBING_AUDIO_DURATION_LIMIT,
  DUBBING_MAX_SENTENCE_LENGTH,
  DUBBING_SOURCE,
  SUBTITLE_FEATURE,
} = require('../constants/dubbing');
const { getFeatureValue } = require('./growthbook');
const { FEATURE_KEYS } = require('../constants/featureKeys');
const { spendSeconds } = require('./secondProcessing');
const { validateLocalLink } = require('../utils/string');
const { RandomFactory } = require('../utils/random');
const { handleDubbingFailure } = require('./ttsProcessing');
const { getAccessToken } = require('./iam');
const { sendEventCreateRequestToMoEngage } = require('./moengage');
const { checkVoicePermission } = require('../utils/tts');
const { getPackageUsageOptions } = require('./package');
const {
  shouldLockCycleCredits,
  shouldLockTopUpCredits,
  shouldLockCustomCredits,
  checkSpamAccount,
} = require('./user');
const { getSynthesisComputePlatform } = require('./synthesisComputePlatform');
const { AuthorizationService } = require('./authorization');

const getSubtitleContent = (content) => {
  const subtitleBlocks = content.split(/\n\s*\n/);
  const parsedSubtitles = [];

  for (const subtitleBlock of subtitleBlocks) {
    /**
     * subtitleBlock: string - subtitle content
     * Example:
        48
        00:03:16,280 --> 00:03:18,907
        <i>Hello, I'm a subtitle.</i>
    */
    const lines = subtitleBlock.trim().split('\n');

    // first line is index, second line is time, third line is subtitle content
    if (lines.length >= 3) {
      const subtitleContent = TextHelper.removeHtmlTags(
        lines.slice(2).join(''),
      );
      parsedSubtitles.push(subtitleContent);
    }
  }

  return parsedSubtitles;
};

const validateVoiceForDubbing = (voice) => {
  if (!voice.hasDubbing) throw new CustomError(code.VOICE_NOT_SUPPORT_DUBBING);
};

const validateTextForDubbing = (plainText, voice) => {
  const { provider, version } = voice;
  const isValidText = validateText({
    text: plainText,
    voiceProvider: provider,
    ttsCoreVersion: version || STUDIO_TTS_VERSION_DEFAULT,
  });

  if (!isValidText) {
    throw new CustomError(code.INVALID_SYNTAX);
  }
};

const validateCharacterForDubbing = (user, characters, blockedCredits) => {
  const { packageExpiryDate, remainingCharacters, bonusCharacters } = user;
  const isLockCycleCredits = blockedCredits.cycleCredits;
  const isLockTopUpCredits = blockedCredits.topUpCredits;
  const isLockCustomCredits = blockedCredits.customCredits;

  if (moment().isAfter(packageExpiryDate, 'day'))
    throw new CustomError(code.PACKAGE_EXPIRED);

  const cycleCredits = isLockCycleCredits
    ? 0
    : user?.studio?.cycle?.remainingCredits || 0;
  const topUpCredits = isLockTopUpCredits
    ? 0
    : user?.studio?.topUp?.remainingCredits || 0;
  const customCredits = isLockCustomCredits
    ? 0
    : user?.studio?.custom?.remainingCredits;

  const oneTimeCredits = remainingCharacters + bonusCharacters;
  const totalCredits =
    cycleCredits + topUpCredits + oneTimeCredits + customCredits;

  if (characters > totalCredits) throw new CustomError(code.TEXT_TOO_LONG);
};

const validateDurationForDubbing = (user, duration = 0) => {
  const { dubbing } = user;
  const { packageExpiryDate, remainingSeconds = 0 } = dubbing || {};

  if (moment().isAfter(packageExpiryDate, 'day'))
    throw new CustomError(code.PACKAGE_EXPIRED);

  if (duration > remainingSeconds)
    throw new CustomError(code.DUBBING_DURATION_TOO_LONG);
};

const getAwsZoneSynthesis = () => {
  if (!MULTI_ZONE) return DEFAULT_AWS_REGION;

  const lengthAwsZones = AWS_ZONES_TTS_STUDIO.length;
  const randomIndex = Math.floor(Math.random() * lengthAwsZones);
  const awsZone = AWS_ZONES_TTS_STUDIO[randomIndex];

  return awsZone || DEFAULT_AWS_REGION;
};

const spendResources = async ({
  userId,
  requestId,
  characters,
  seconds,
  isProcessDubbingByUnitSecond,
  blockedCredits,
}) => {
  if (isProcessDubbingByUnitSecond)
    await spendSeconds({ userId, requestId, seconds });
  else await spendCharacters({ userId, requestId, characters, blockedCredits });
};

const validateSubtitles = async (subtitleLink) => {
  // Get subtitle content
  const fileContent = await readFileFromLink(subtitleLink);
  const subtitlesContent = getSubtitleContent(fileContent);
  const plainText = subtitlesContent.join(' ');

  // Validate srt format
  const { error, message, parsedSubtitles } = parseSRT(fileContent);
  if (error) throw new CustomError(code.INVALID_SRT_FORMAT, message);

  // Validate subtitle language
  const isVietnameseSubtitle = await VietnameseHelper.isVietnameseText(
    plainText,
  );
  if (!isVietnameseSubtitle)
    throw new CustomError(code.DUBBING_ONLY_SUPPORT_VIETNAMESE);

  // Validate sentence length
  const sentenceTooLong = parsedSubtitles.some(
    (subtitle) => subtitle.content?.length > DUBBING_MAX_SENTENCE_LENGTH,
  );
  if (sentenceTooLong) throw new CustomError(code.DUBBING_SENTENCE_TOO_LONG);

  return { fileContent, subtitlesContent, parsedSubtitles, plainText };
};

const validateVoice = async ({
  voiceCode,
  user,
  isProcessDubbingByUnitSecond,
}) => {
  // Validate voice
  const voice = await getVoiceByCode(voiceCode);
  const studioUsageOptions = await getPackageUsageOptions({
    userId: user._id,
    packageCode: user.packageCode,
    userUsageOptions: user,
  });
  const dubbingUsageOptions = await getPackageUsageOptions({
    userId: user._id,
    packageCode: user?.dubbing?.packageCode,
    userUsageOptions: user?.dubbing,
  });
  const studioFeatures = studioUsageOptions?.features;
  const dubbingFeatures = dubbingUsageOptions?.features;
  const userFeatures =
    (isProcessDubbingByUnitSecond ? dubbingFeatures : studioFeatures) || [];
  const isValidVoice = checkVoicePermission(
    userFeatures,
    voice?.features || [],
  );
  if (!isValidVoice) throw new CustomError(code.UNAVAILABLE_VOICE);

  validateVoiceForDubbing(voice);

  return voice;
};

const validateMultipleVoices = async ({
  voiceCodes,
  user,
  isProcessDubbingByUnitSecond,
}) => {
  const codes = Object.keys(voiceCodes);
  const voices = await Promise.all(
    codes.map(async (voiceCode) => {
      const voice = await validateVoice({
        voiceCode,
        user,
        isProcessDubbingByUnitSecond,
      });

      return voice;
    }),
  );

  return voices;
};

const validateAudio = async ({
  subtitleDuration,
  isProcessDubbingByUnitSecond,
}) => {
  // Validate audio duration
  const seconds = isProcessDubbingByUnitSecond
    ? Math.ceil(subtitleDuration / 1000)
    : null; // null instead of 0 because we don't want to save this field to db in case process by unit character
  if (subtitleDuration > DUBBING_AUDIO_DURATION_LIMIT * 3600 * 1000)
    throw new CustomError(code.DUBBING_AUDIO_TOO_LONG);

  return seconds;
};

const getSeconds = (subtitleDuration) => {
  // Validate audio duration
  const seconds = Math.ceil(subtitleDuration / 1000);
  if (subtitleDuration > DUBBING_AUDIO_DURATION_LIMIT * 3600 * 1000)
    throw new CustomError(code.DUBBING_AUDIO_TOO_LONG);

  return seconds;
};

const validateUserPackage = async ({
  user,
  characters,
  userId,
  isProcessDubbingByUnitSecond,
  seconds,
  blockedCredits,
}) => {
  if (!user.firstConvertAt) {
    userDao
      .updateUserById(userId, { firstConvertAt: new Date() })
      .catch((err) => {
        logger.error(err, { ctx: 'UpdateFirstConvertAt' });
      });
  }

  if (isProcessDubbingByUnitSecond) validateDurationForDubbing(user, seconds);
  else validateCharacterForDubbing(user, characters, blockedCredits);

  return user;
};

const handleDubbingWithVideo = async ({
  ip,
  userId,
  email,
  phoneNumber,
  title,
  createdAt = new Date(),
  speed = 1,
  bitrate,
  voiceCode,
  audioType = DEFAULT_AUDIO_TYPE,
  linkVideo,
  source,
  videoDuration,
  originalLanguage,
}) => {
  const isMultipleInputDubbing = getFeatureValue(
    FEATURE_KEYS.MULTIPLE_INPUT_DUBBING,
    { userId, email, phoneNumber },
  );

  if (!isMultipleInputDubbing) {
    logger.error('Multiple input dubbing is not enabled', {
      ctx: 'HandleDubbing',
      userId,
      email,
    });
    throw new CustomError(
      code.BAD_REQUEST,
      'Multiple input dubbing is not enabled',
    );
  }

  if (source !== DUBBING_SOURCE.LOCAL && source !== DUBBING_SOURCE.YOUTUBE) {
    logger.error('Invalid source', { ctx: 'HandleDubbing', userId, email });
    throw new CustomError(code.BAD_REQUEST, 'Invalid source');
  }

  if (source !== DUBBING_SOURCE.SRT && !videoDuration) {
    logger.error('Video duration is required', {
      ctx: 'HandleDubbing',
      userId,
      email,
    });
    throw new CustomError(code.BAD_REQUEST, 'Video duration is required');
  }

  if (
    source === DUBBING_SOURCE.YOUTUBE &&
    !TextHelper.isYoutubeLink(linkVideo)
  ) {
    logger.error('Invalid youtube link', {
      ctx: 'HandleDubbing',
      userId,
      email,
    });
    throw new CustomError(code.BAD_REQUEST, 'Invalid youtube link');
  }

  if (source === DUBBING_SOURCE.LOCAL && !validateLocalLink(linkVideo)) {
    logger.error('Invalid local link', { ctx: 'HandleDubbing', userId, email });
    throw new CustomError(code.BAD_REQUEST, 'Invalid local link');
  }

  const requestId = RandomFactory.getGuid();
  const isProcessDubbingByUnitSecond = getFeatureValue(
    FEATURE_KEYS.DUBBING_BY_UNIT_SECOND,
    { userId, email, phoneNumber },
  );

  const user = await findUser({ _id: userId });

  // Validate voice
  const voice = await validateVoice({
    voiceCode,
    user,
    isProcessDubbingByUnitSecond,
  });

  // Validate audio duration
  const seconds = await validateAudio({
    subtitleDuration: videoDuration,
    isProcessDubbingByUnitSecond,
  });

  const awsZoneSynthesis = getAwsZoneSynthesis();
  const awsZone = (await getAwsZone({ region: awsZoneSynthesis })) || {};
  const {
    normalizerFunction,
    sentenceTokenizerFunction,
    newSentenceTokenizerFunction,
    textToAllophoneFunction,
    synthesisFunction,
    joinSentencesFunction,
    srtFunction,
    defaultS3Bucket,
    s3Buckets = {},
  } = awsZone;

  // Save request to database
  const { retentionPeriod = DEFAULT_RETENTION_PERIOD } = user?.dubbing || {};
  const packageCode = isProcessDubbingByUnitSecond
    ? user?.dubbing?.packageCode
    : user?.packageCode;
  const request = {
    ip,
    userId,
    requestId,
    title,
    text: '',
    seconds,
    subtitleLink: '',
    audioType,
    speed,
    createdAt,
    status: REQUEST_STATUS.IN_PROGRESS,
    voiceCode,
    bitrate,
    sampleRate: voice.defaultSampleRate.toString(),
    retentionPeriod,
    type: REQUEST_TYPE.DUBBING,
    version: voice.version,
    serviceType: SERVICE_TYPE.AI_VOICE,
    packageCode,
    awsZoneSynthesis,
    awsZoneFunctions: {
      normalizerFunction,
      sentenceTokenizerFunction,
      newSentenceTokenizerFunction,
      textToAllophoneFunction,
      synthesisFunction,
      joinSentencesFunction,
      srtFunction,
      s3Bucket: s3Buckets[retentionPeriod] || defaultS3Bucket,
    },
  };
  await createRequest(request);

  const getLinkSrtMessage = {
    value: {
      requestId,
      linkVideo,
      source,
      email,
      phoneNumber,
      userId,
      seconds,
      voice,
      originalLanguage,
    },
  };

  sendMessage(KAFKA_TOPIC.GET_LINK_SRT_FILE_REQUEST, getLinkSrtMessage);
  delete request.voiceCode;
  delete request.awsZoneFunctions;
  delete request.awsZoneSynthesis;

  return request;
};

const handleGetLinkSrtFileSuccess = async ({
  requestId,
  subtitleLink,
  email,
  phoneNumber,
  userId,
  seconds,
  voice,
}) => {
  const isProcessDubbingByUnitSecond = getFeatureValue(
    FEATURE_KEYS.DUBBING_BY_UNIT_SECOND,
    { userId, email, phoneNumber },
  );

  // Validate subtitle
  const { fileContent, subtitlesContent, plainText } = await validateSubtitles(
    subtitleLink,
  );
  const characters = subtitlesContent.join('').length;

  // Validate user package
  const user = await validateUserPackage({
    characters,
    userId,
    isProcessDubbingByUnitSecond,
    seconds,
  });
  const { concurrentRequest } = user;

  // Validate text
  validateTextForDubbing(plainText, voice);

  const request = await requestDao.findRequestById(requestId);
  if (!request) throw new CustomError(code.REQUEST_NOT_FOUND);

  const updateRequest = {
    text: fileContent,
    characters,
    subtitleLink,
    seconds,
  };
  await requestDao.updateRequestById(requestId, updateRequest);
  const cacheRequest = {
    ...request,
    numberOfSentences: 0,
    voice,
    progress: 0,
    sentenceKeys: [`${REDIS_KEY_PREFIX.SENTENCE}_${requestId}_${0}`],
    numberOfIndexSentences: 1,
  };

  await createRequestInRedis(cacheRequest);

  const processRequestStatus = await pushRequestToQueue({
    userId,
    requestId,
    ccr: concurrentRequest,
    requestType: REQUEST_TYPE.DUBBING,
  });
  if (!processRequestStatus)
    throw new CustomError(
      code.INTERNAL_SERVER_ERROR,
      'Cannot push request to queue',
    );
  spendResources({
    userId,
    requestId,
    characters,
    seconds,
    isProcessDubbingByUnitSecond,
  });
  sendMessage(KAFKA_TOPIC.TTS_CREATED, {
    value: {
      requestId,
      userId,
      characters: isProcessDubbingByUnitSecond ? undefined : characters,
      seconds: isProcessDubbingByUnitSecond ? seconds : undefined,
    },
  });
};

const handleGetLinkSrtFileFail = async ({ requestId, errorMessage }) => {
  const request = await requestDao.findRequestById(requestId);
  if (!request) throw new CustomError(code.REQUEST_NOT_FOUND);
  await requestDao.updateRequestById(requestId, {
    status: REQUEST_STATUS.FAILURE,
  });
  handleDubbingFailure({
    request,
    errorCode: code.DUBBING_FAILURE,
    errorMessage,
  });
};

const getResourceProcess = ({ user, seconds, studioUsageOptions }) => {
  const { dubbing } = user;
  const studioFeatures = studioUsageOptions?.features || user.features;

  const checkEnoughSeconds =
    dubbing?.remainingSeconds >= seconds &&
    dubbing?.packageExpiryDate > new Date();
  if (checkEnoughSeconds) return RESOURCE_TYPE.SECONDS;

  if (studioFeatures.includes(SUBTITLE_FEATURE))
    return RESOURCE_TYPE.CHARACTERS;

  return RESOURCE_TYPE.SECONDS;
};

const handleDubbing = async ({
  ip,
  device,
  deviceInfo,
  userId,
  email,
  phoneNumber,
  title,
  createdAt = new Date(),
  speed = 1,
  bitrate,
  voiceCode,
  audioType = DEFAULT_AUDIO_TYPE,
  subtitleLink,
  projectId,
  sentencesVoiceCode = {},
}) => {
  const blockedCredits = {};
  const requestId = RandomFactory.getGuid();
  let isProcessDubbingByUnitSecond = getFeatureValue(
    FEATURE_KEYS.DUBBING_BY_UNIT_SECOND,
    { userId, email, phoneNumber },
  );

  const user = await findUser({ _id: userId });
  const isSpamAccount = await checkSpamAccount({
    email,
    ip,
    packageCode: user?.packageCode,
    userId,
    feature: PACKAGE_FEATURE.SUBTITLE,
  });
  if (isSpamAccount) throw new CustomError(code.SPAM_ACCOUNT);

  const studioUsageOptions = await getPackageUsageOptions({
    userId,
    packageCode: user.packageCode,
    userUsageOptions: user,
  });

  const isMultipleVoices = Object.keys(sentencesVoiceCode).length > 0;

  // Validate subtitle
  const { fileContent, subtitlesContent, parsedSubtitles, plainText } =
    await validateSubtitles(subtitleLink);
  const characters = subtitlesContent.join('').length;

  const subtitleDuration = getSRTDuration(parsedSubtitles);
  const seconds = getSeconds(subtitleDuration);
  const resourceType = getResourceProcess({
    user,
    seconds,
    studioUsageOptions,
  });

  // Process by unit second if user has enough seconds
  isProcessDubbingByUnitSecond = resourceType === RESOURCE_TYPE.SECONDS;

  // Validate audio duration
  await validateAudio({
    subtitleDuration,
    isProcessDubbingByUnitSecond,
  });

  blockedCredits.cycleCredits = shouldLockCycleCredits({ user });
  blockedCredits.topUpCredits = shouldLockTopUpCredits({ user });
  blockedCredits.customCredits = shouldLockCustomCredits(user);

  // Validate user package
  await validateUserPackage({
    user,
    characters,
    userId,
    isProcessDubbingByUnitSecond,
    seconds,
    blockedCredits,
  });

  let voice;

  if (isMultipleVoices) {
    // Validate voices
    const voices = await validateMultipleVoices({
      voiceCodes: sentencesVoiceCode,
      user,
      isProcessDubbingByUnitSecond,
    });

    // Validate text with each voice
    voices.forEach((voiceInfo) => validateTextForDubbing(plainText, voiceInfo));

    // Get first voice to save to request
    [voice] = voices;
  } else {
    // Validate voice
    voice = await validateVoice({
      voiceCode,
      user,
      isProcessDubbingByUnitSecond,
    });

    // Validate text
    validateTextForDubbing(plainText, voice);
  }

  const awsZoneSynthesis = getAwsZoneSynthesis();
  const awsZone = (await getAwsZone({ region: awsZoneSynthesis })) || {};
  const {
    normalizerFunction,
    sentenceTokenizerFunction,
    newSentenceTokenizerFunction,
    textToAllophoneFunction,
    synthesisFunction,
    joinSentencesFunction,
    srtFunction,
    defaultS3Bucket,
    s3Buckets = {},
  } = awsZone;

  const dubbingUsageOptions = await getPackageUsageOptions({
    userId,
    packageCode: user?.dubbing?.packageCode,
    userUsageOptions: user?.dubbing,
  });

  // Save request to database
  const { retentionPeriod = DEFAULT_RETENTION_PERIOD, concurrentRequest = 1 } =
    isProcessDubbingByUnitSecond ? dubbingUsageOptions : studioUsageOptions;
  const packageCode = isProcessDubbingByUnitSecond
    ? user?.dubbing?.packageCode
    : user?.packageCode;
  const request = {
    ip,
    device,
    deviceInfo,
    userId,
    requestId,
    title,
    text: fileContent,
    characters,
    seconds,
    subtitleLink,
    audioType,
    speed,
    createdAt,
    status: REQUEST_STATUS.IN_PROGRESS,
    voiceCode,
    bitrate,
    sampleRate: voice.defaultSampleRate.toString(),
    retentionPeriod,
    type: REQUEST_TYPE.DUBBING,
    version: voice.version,
    serviceType: SERVICE_TYPE.AI_VOICE,
    packageCode,
    awsZoneSynthesis,
    awsZoneFunctions: {
      normalizerFunction,
      sentenceTokenizerFunction,
      newSentenceTokenizerFunction,
      textToAllophoneFunction,
      synthesisFunction,
      joinSentencesFunction,
      srtFunction,
      s3Bucket: s3Buckets[retentionPeriod] || defaultS3Bucket,
    },
  };

  request.synthesisComputePlatform = getSynthesisComputePlatform(request);

  if (projectId) request.projectId = projectId;

  if (isMultipleVoices) request.sentencesVoiceCode = sentencesVoiceCode;

  await require('./request').createRequest(request);

  // Update project
  if (projectId) {
    const projectUpdateFields = {
      title,
      voiceCode,
      speed,
      currentSubtitleLink: subtitleLink,
      latestRequestId: requestId,
      status: REQUEST_STATUS.IN_PROGRESS,
    };
    if (isMultipleVoices)
      projectUpdateFields.sentencesVoiceCode = sentencesVoiceCode;

    await updateProject(projectId, projectUpdateFields);
  }

  const cacheRequest = {
    ...request,
    numberOfSentences: 0,
    progress: 0,
    sentenceKeys: [`${REDIS_KEY_PREFIX.SENTENCE}_${requestId}_${0}`],
    numberOfIndexSentences: 1,
    voice,
  };

  // ADVISE: avoid using inline require()
  await require('./request').createRequestInRedis(cacheRequest);

  const processRequestStatus = await pushRequestToQueue({
    userId,
    requestId,
    ccr: concurrentRequest,
    requestType: REQUEST_TYPE.DUBBING,
  });
  if (!processRequestStatus)
    throw new CustomError(
      code.INTERNAL_SERVER_ERROR,
      'Cannot push request to queue',
    );
  spendResources({
    userId,
    requestId,
    characters,
    seconds,
    isProcessDubbingByUnitSecond,
    blockedCredits,
  });

  sendMessage(KAFKA_TOPIC.TTS_CREATED, {
    value: {
      requestId,
      userId,
      characters: isProcessDubbingByUnitSecond ? undefined : characters,
      seconds: isProcessDubbingByUnitSecond ? seconds : undefined,
      blockedCredits,
    },
  });

  // Don't use await so it wouldn't affect the response time
  sendEventCreateRequestToMoEngage({ customerId: userId, request });

  delete request.voiceCode;
  delete request.awsZoneFunctions;
  delete request.awsZoneSynthesis;
  return request;
};

const countSubtitleCharacters = async (subtitleLink) => {
  const fileContent = await readFileFromLink(subtitleLink);
  const subtitlesContent = getSubtitleContent(fileContent);
  const characters = subtitlesContent.join('').length;

  return characters;
};

const processTtsDubbingToJoinAudios = async (requestId) => {
  const ttsList = await getTtsFromRequestIdInRedis(requestId);

  const subtitles = ttsList.map((tts) => ({
    start: tts.start,
    end: tts.end,
    audio_name: tts.audioName,
  }));

  return subtitles;
};

const handlePayOrder = async ({
  userId,
  orderId,
  seconds = 0,
  userRemainingSeconds = 0,
  userLockSeconds = 0,
  isFreePackage,
  isGift,
}) => {
  let userUpdateFields = {};
  if (isFreePackage) {
    userUpdateFields = {
      'dubbing.remainingSeconds': seconds,
    };
  } else
    userUpdateFields = {
      'dubbing.remainingSeconds':
        userRemainingSeconds + userLockSeconds + seconds,
      'dubbing.lockSeconds': 0,
    };

  await orderDao.updateOrderById(orderId, {
    userId,
    payOrder: true,
    type: PACKAGE_TYPE.DUBBING,
  });
  const user = await userDao.updateUserById(userId, userUpdateFields);

  const remainingSeconds = user?.dubbing?.remainingSeconds;
  const lockSeconds = user?.dubbing?.lockSeconds || 0;

  sendMessage(KAFKA_TOPIC.ORDER_PAID, {
    value: {
      userId,
      type: REQUEST_TYPE.DUBBING,
      remainingSeconds: Number(remainingSeconds),
      lockSeconds: Number(lockSeconds),
      isGift,
    },
  });
};

const handleCancelOrder = async ({
  userId,
  orderId,
  userRemainingSeconds = 0,
  userLockSeconds = 0,
  seconds = 0,
  beforePackage,
}) => {
  const hasBeforePackageExpired =
    beforePackage?.packageExpiryDate &&
    moment(beforePackage.packageExpiryDate).isBefore(moment());

  const hasBeforePackageFree = [
    PACKAGE_CODE.DUBBING_BASIC,
    PACKAGE_CODE.DUBBING_TRIAL,
  ].includes(beforePackage?.packageCode);

  let updatePaidSeconds = hasBeforePackageExpired
    ? userRemainingSeconds + userLockSeconds - seconds
    : userRemainingSeconds - seconds;

  updatePaidSeconds = updatePaidSeconds < 0 ? 0 : updatePaidSeconds;

  const userUpdateFields = {};

  if (beforePackage) {
    Object.keys(beforePackage).forEach((key) => {
      userUpdateFields[`dubbing.${key}`] = beforePackage[key];
    });
    // If before package is expired then set lock characters
    if (hasBeforePackageExpired) {
      userUpdateFields['dubbing.lockSeconds'] = updatePaidSeconds;
      userUpdateFields['dubbing.remainingSeconds'] = 0;
    } else if (hasBeforePackageFree) {
      userUpdateFields['dubbing.remainingSeconds'] = 0;
      userUpdateFields['dubbing.lockSeconds'] = updatePaidSeconds;
    } else {
      userUpdateFields['dubbing.remainingSeconds'] = updatePaidSeconds;
    }
  }

  await orderDao.updateOrderById(orderId, { cancelOrder: true });
  await userDao.updateUserById(userId, userUpdateFields);

  // TODO: send notification to user
};

const handleSpendSeconds = async ({
  userId,
  requestId,
  seconds,
  remainingSeconds: currentRemainingSeconds,
}) => {
  const remainingSeconds = currentRemainingSeconds - seconds;

  await requestDao.updateRequestById(requestId, { paid: true });
  await userDao.updateUserById(userId, {
    'dubbing.remainingSeconds': remainingSeconds,
  });
};

const handleRefundSeconds = async ({
  userId,
  requestId,
  seconds,
  remainingSeconds: currRemainingSeconds,
}) => {
  const remainingSeconds = currRemainingSeconds + seconds;

  await requestDao.updateRequestById(requestId, { refund: true });
  await userDao.updateUserById(userId, {
    'dubbing.remainingSeconds': remainingSeconds,
  });
};

const handleLockSeconds = async (userId, userRemainingSeconds) => {
  await userDao.updateUserById(userId, {
    'dubbing.remainingSeconds': 0,
    'dubbing.lockSeconds': userRemainingSeconds,
    'dubbing.lockedSecondsAt': new Date(),
  });
};

const handleResetBonusSeconds = async ({
  userId,
  orderId,
  bonusSeconds,
  remainingSeconds,
}) => {
  await orderDao.updateOrderById(orderId, {
    userId,
    lastResetBonusDubbingAt: new Date(),
    type: PACKAGE_TYPE.DUBBING,
  });
  if (bonusSeconds > remainingSeconds) {
    await userDao.updateUserById(userId, {
      'dubbing.remainingSeconds': bonusSeconds,
    });
  }
};

const updateBySeconds = async ({
  event,
  userId,
  orderId,
  requestId,
  seconds,
  isFreePackage,
  beforePackage,
  bonusSeconds,
  isGift,
}) => {
  const user = await AuthorizationService.getUser(userId, false);
  // ADVISE: can we return ASAP when there is no user?

  switch (event) {
    case SYNC_SECONDS_EVENT.PAY_ORDER: {
      const order = await orderDao.findOrder({ _id: orderId });
      if (order && order.payOrder) break;

      const userRemainingSeconds = user?.dubbing?.remainingSeconds || 0;
      const userLockSeconds = user?.dubbing?.lockSeconds || 0;
      await handlePayOrder({
        userId,
        orderId,
        seconds,
        userRemainingSeconds,
        userLockSeconds,
        isFreePackage,
        isGift,
      });
      break;
    }

    case SYNC_SECONDS_EVENT.CANCEL_ORDER_AFTER_PAID: {
      const order = await orderDao.findOrder({ _id: orderId });
      if (order?.cancelOrder) break;

      const userRemainingSeconds = user?.dubbing?.remainingSeconds || 0;
      const userLockSeconds = user?.dubbing?.lockSeconds || 0;

      await handleCancelOrder({
        userId,
        orderId,
        userRemainingSeconds,
        userLockSeconds,
        seconds,
        beforePackage,
      });
      break;
    }

    case SYNC_SECONDS_EVENT.SPEND: {
      if (!user) {
        logger.error('User not found', { userId, ctx: 'UpdateBySeconds' });
        return;
      }

      const request = await requestDao.findRequestById(requestId);
      if (!request) {
        logger.error('Request not found', {
          requestId,
          ctx: 'UpdateBySeconds',
        });
        return;
      }
      if (request.paid) break;
      const remainingSeconds = user?.dubbing?.remainingSeconds || 0;

      await handleSpendSeconds({
        userId,
        requestId,
        seconds,
        remainingSeconds,
      });
      break;
    }

    case SYNC_SECONDS_EVENT.REFUND: {
      if (!user) {
        logger.error('User not found', { userId, ctx: 'UpdateBySeconds' });
        return;
      }

      const request = await requestDao.findRequestById(requestId);
      if (!request) {
        logger.error('Request not found', {
          requestId,
          ctx: 'UpdateBySeconds',
        });
        return;
      }
      if (request.refund) break;
      const remainingSeconds = user?.dubbing?.remainingSeconds || 0;

      await handleRefundSeconds({
        userId,
        requestId,
        seconds: request.seconds || 0,
        remainingSeconds,
      });
      break;
    }

    case SYNC_SECONDS_EVENT.LOCK_SECONDS: {
      if (!user) {
        logger.error('User not found', { userId, ctx: 'UpdateBySeconds' });
        break;
      }

      const userLockedSecondsAt = user?.dubbing?.lockedSecondsAt;
      const isDuplicate =
        userLockedSecondsAt && moment().isSame(userLockedSecondsAt, 'day');
      if (isDuplicate) break;

      const userRemainingSeconds = isFreePackage
        ? 0
        : user?.dubbing?.remainingSeconds || 0;
      await handleLockSeconds(userId, userRemainingSeconds);

      break;
    }

    case SYNC_SECONDS_EVENT.RESET_BONUS_SECONDS: {
      if (!user) {
        logger.error('User not found', { userId, ctx: 'UpdateBySeconds' });
        break;
      }

      const order = await orderDao.findOrder({ _id: orderId });

      const isDuplicate =
        order?.lastResetBonusDubbingAt &&
        moment().diff(order.lastResetBonusDubbingAt, 'days') <= 30;

      logger.info(`Order has been reset this month: ${isDuplicate}`, {
        ctx: 'UpdateBySeconds',
        userId,
        lastResetBonusDubbingAt: order?.lastResetBonusDubbingAt,
      });
      if (isDuplicate) break;

      await handleResetBonusSeconds({
        userId,
        orderId,
        bonusSeconds,
        remainingSeconds: user?.dubbing?.remainingSeconds || 0,
      });

      break;
    }

    default:
      return;
  }

  // Sync user remaining seconds
  const updatedUser = await AuthorizationService.getUser(userId, false);
  // ADVISE: can we throw if user not found? there nowhere to update

  const remainingSeconds = updatedUser?.dubbing?.remainingSeconds || 0;
  const lockSeconds = updatedUser?.dubbing?.lockSeconds || 0;

  const syncData = {
    event,
    userId,
    orderId,
    remainingSeconds,
    lockSeconds,
    syncCharactersAt: new Date(),
  };

  if (event === SYNC_SECONDS_EVENT.PAY_ORDER)
    syncData.totalDubbingSeconds = remainingSeconds;

  sendMessage(KAFKA_TOPIC.SYNC_SECONDS, {
    key: userId,
    value: syncData,
  });
};

const createProject = async ({
  userId,
  title,
  speed,
  voiceCode,
  subtitleLink,
  originalInfo,
  targetLanguage,
}) => {
  try {
    const isMultipleInputDubbing = getFeatureValue(
      FEATURE_KEYS.MULTIPLE_INPUT_DUBBING,
      { userId },
    );

    const projectData = {
      userId,
      title,
      speed,
      voiceCode,
      currentSubtitleLink: subtitleLink,
    };

    if (isMultipleInputDubbing) {
      projectData.originalInfo = originalInfo;
      projectData.targetLanguage = targetLanguage;
    }

    const accessToken = await getAccessToken();
    const { status, result } = await callApi({
      url: `${VBEE_DUBBING_URL}/api/v1/projects`,
      method: 'POST',
      data: projectData,
      headers: { Authorization: `Bearer ${accessToken}` },
    });

    if (status !== 1) throw new Error('Failed to create dubbing project');
    return result;
  } catch (err) {
    logger.error(err, { ctx: 'CreateProject', userId });
    throw new CustomError(
      code.INTERNAL_SERVER_ERROR,
      'Failed to create dubbing project',
    );
  }
};

const getListProjects = async ({
  userId,
  search,
  fields,
  offset,
  limit,
  sort,
  status: projectStatus,
  startDate,
  endDate,
}) => {
  try {
    const accessToken = await getAccessToken();
    const { status, result } = await callApi({
      url: `${VBEE_DUBBING_URL}/api/v1/projects`,
      method: 'GET',
      params: {
        userId,
        search,
        fields,
        offset,
        limit,
        sort,
        status: projectStatus,
        startDate,
        endDate,
      },
      headers: { Authorization: `Bearer ${accessToken}` },
    });

    if (status !== 1) throw new Error('Failed to get list projects');
    return result;
  } catch (err) {
    logger.error(err, { ctx: 'GetListProjects', userId });
    throw new CustomError(
      code.INTERNAL_SERVER_ERROR,
      'Failed to get list projects',
    );
  }
};

const updateProject = async (projectId, updatedFields) => {
  try {
    const accessToken = await getAccessToken();
    const { status, result } = await callApi({
      url: `${VBEE_DUBBING_URL}/api/v1/projects/${projectId}`,
      method: 'PUT',
      data: updatedFields,
      headers: { Authorization: `Bearer ${accessToken}` },
    });

    if (status !== 1) throw new Error('Failed to update project');
    return result;
  } catch (err) {
    logger.error(err, { ctx: 'UpdateProject', projectId });
    throw new CustomError(
      code.INTERNAL_SERVER_ERROR,
      'Failed to update project',
    );
  }
};

const updateProjectStatusFromRequest = async ({
  projectId,
  requestStatus,
  requestId,
  userId,
}) => {
  try {
    const accessToken = await getAccessToken();
    const { status, result } = await callApi({
      url: `${VBEE_DUBBING_URL}/api/v1/projects/${projectId}/status`,
      method: 'PUT',
      data: { status: requestStatus, requestId, userId },
      headers: { Authorization: `Bearer ${accessToken}` },
    });

    if (status !== 1) throw new Error();
    return result;
  } catch (err) {
    logger.error(err, { ctx: 'UpdateProjectStatus', projectId });
    throw new CustomError(code.UPDATE_PROJECT_STATUS_FAILURE);
  }
};

const callApiDubbingDeleteProjects = async ({
  userId,
  projectIds,
  isDeleteAll,
}) => {
  try {
    const accessToken = await getAccessToken();
    const { status, result } = await callApi({
      url: `${VBEE_DUBBING_URL}/api/v1/projects`,
      method: 'DELETE',
      data: {
        userId,
        projectIds,
        isDeleteAll,
      },
      headers: { Authorization: `Bearer ${accessToken}` },
    });

    if (status !== 1) throw new CustomError(code.INTERNAL_SERVER_ERROR);
    return result;
  } catch (err) {
    logger.error(err, { ctx: 'DeleteProjects', userId });
    throw new CustomError(
      code.INTERNAL_SERVER_ERROR,
      'Failed to delete projects',
    );
  }
};

const deleteProjects = async ({ userId, projectIds, isDeleteAll }) => {
  await callApiDubbingDeleteProjects({
    userId,
    projectIds,
    isDeleteAll,
  });

  if (isDeleteAll) await requestDao.hideAllRequestsInProjectByUser(userId);
  else
    await requestDao.hideRequestsByProjectIds({
      userId,
      projectIds,
    });
};

const callApiDubbingGetProject = async ({ projectId, userId }) => {
  try {
    const accessToken = await getAccessToken();
    const { status, result } = await callApi({
      url: `${VBEE_DUBBING_URL}/api/v1/projects/${projectId}`,
      method: 'GET',
      params: { userId },
      headers: { Authorization: `Bearer ${accessToken}` },
    });

    if (status !== 1) throw new Error();
    return result;
  } catch (err) {
    logger.error(err, { ctx: 'GetProject', projectId, userId });
    throw new CustomError(code.GET_PROJECT_FAILURE, 'Failed to get project');
  }
};

const getProject = async ({ projectId, userId }) => {
  const project = await callApiDubbingGetProject({ projectId, userId });
  if (!project)
    throw new CustomError(code.GET_PROJECT_FAILURE, 'Project not found');

  return project;
};

const getLanguages = async (query) => {
  const { status, result } = await callApi({
    url: `${VBEE_DUBBING_URL}/api/v1/languages`,
    method: 'GET',
    params: query,
  });

  if (status !== 1) throw new CustomError(code.INTERNAL_SERVER_ERROR);

  return result;
};

module.exports = {
  handleDubbing,
  countSubtitleCharacters,
  processTtsDubbingToJoinAudios,
  updateBySeconds,
  handleDubbingWithVideo,
  handleGetLinkSrtFileSuccess,
  handleGetLinkSrtFileFail,
  createProject,
  getListProjects,
  updateProject,
  deleteProjects,
  getProject,
  updateProjectStatusFromRequest,
  getLanguages,
};

/* eslint-disable array-callback-return */
const camelCaseKeys = require('camelcase-keys');
require('dotenv').config();

const kafka = require('./kafka');
const { getFeatureValue } = require('../growthbook');
const {
  KAFKA_CONSUMER_GROUP_TTS,
  SYNTHESIS_BY_GATEWAY,
  SERVICE,
} = require('../../configs');
const {
  KAFKA_TOPIC,
  SWITCH_VERSION_EVENT,
  REQUEST_STATUS,
} = require('../../constants');
const { FEATURE_KEYS } = require('../../constants/featureKeys');

const logger = require('../../utils/logger');
const {
  handleSentenceTokenizationSuccessResponse,
  handleSentenceTokenizationFailureResponse,
  handleSynthesisSuccessResponse,
  handleSynthesisFailureResponse,
  handleJoinAudiosSuccessResponse,
  handleJoinAudiosFailureResponse,
} = require('../synthesis');
const {
  updateByNewPackage,
  updateByCharacters,
  migrateAccount,
  handleMigrateDubbingToStudio,
} = require('../user');
const { syncDictionary } = require('../dictionary');
const {
  handleTtsDemoResponse,
  handleRequestTimeOut,
  handleUpdateProgressRequest,
} = require('../ttsProcessing');
const { sendApiResponse } = require('../apiResponse');
const {
  handleCachingSynthesisSuccess,
  handleCachingSynthesisFailure,
} = require('../cachingSynthesis');

const { updateUserById } = require('../../daos/user');
const { calculateMetricBasedOnKafkaTopic } = require('../metrics');
const {
  updateBySeconds,
  handleGetLinkSrtFileSuccess,
  handleGetLinkSrtFileFail,
} = require('../dubbing');
const { updateSttSeconds } = require('../stt/sttProcessing');

global.KAFKA_CONSUMER_LISTS = [];

// ADVISE: this a remote config (only depend on the FF key and hardcode SERVICE). Should be in a centralized ConfigService (so it is cachable, fallback, ...)
// ADVISE: when a service is always hardcode as SERVICE, why don't it be inside getFeatureValue(), so we don't need to pass it every time?
const enabledKafkaHealthcheck = getFeatureValue(
  FEATURE_KEYS.KAFKA_HEALTHCHECK,
  { service: SERVICE },
);

const addConsumerEventHandlers = (newConsumer) => {
  logger.warn('addConsumerEventHandlers', { ctx: 'KafkaConsume' });
  KAFKA_CONSUMER_LISTS.push(newConsumer);
  const consumerStartAt = Date.now();
  const { HEARTBEAT, CONNECT, GROUP_JOIN, CRASH, STOP, DISCONNECT } =
    newConsumer.events;

  // debug log for status checking
  newConsumer.on(HEARTBEAT, ({ payload }) => {
    logger.debug('KAFKA_HEARTBEAT', { ctx: 'KafkaConsume', payload });
  });
  // state ok
  newConsumer.on(CONNECT, () => {
    newConsumer.isReady = false;
    newConsumer.isHealthy = true;
  });
  newConsumer.on(GROUP_JOIN, ({ payload }) => {
    newConsumer.isReady = true;
    newConsumer.isHealthy = true;
    const consumerJoinAt = Date.now();
    const consumerDuration = (consumerJoinAt - consumerStartAt) / 1000;
    logger.debug(
      { ...payload, consumerDuration: `${consumerDuration}s` },
      { ctx: 'KafkaConsume' },
    );
  });
  // state error
  newConsumer.on(CRASH, ({ payload }) => {
    newConsumer.isReady = false;
    newConsumer.isHealthy = payload.restart || false;
    logger.warn(payload, { ctx: 'KafkaConsume' });
  });
  newConsumer.on(STOP, () => {
    newConsumer.isReady = false;
    newConsumer.isHealthy = false;
    logger.warn({ event: 'Kafka consumer stopped' }, { ctx: 'KafkaConsume' });
  });
  newConsumer.on(DISCONNECT, () => {
    newConsumer.isReady = false;
    newConsumer.isHealthy = false;
    logger.warn(
      { event: 'Kafka consumer disconnected' },
      { ctx: 'KafkaConsume' },
    );
  });
  return newConsumer;
};

const consumer = kafka.consumer({ groupId: KAFKA_CONSUMER_GROUP_TTS });
if (enabledKafkaHealthcheck) addConsumerEventHandlers(consumer);
const publishConsumer = kafka.consumer({
  groupId: `${KAFKA_CONSUMER_GROUP_TTS}-${KAFKA_CONSUMER_GROUP_RANDOM_TTS}`,
});
if (enabledKafkaHealthcheck) addConsumerEventHandlers(publishConsumer);

const consume = async () => {
  await consumer.connect();
  const synthesisTopics = [
    KAFKA_TOPIC.SENTENCE_TOKENIZATION_SUCCESS,
    KAFKA_TOPIC.SENTENCE_TOKENIZATION_FAILURE,
    KAFKA_TOPIC.SYNTHESIS_SUCCESS,
    KAFKA_TOPIC.SYNTHESIS_FAILURE,
    KAFKA_TOPIC.JOIN_AUDIOS_SUCCESS,
    KAFKA_TOPIC.JOIN_AUDIOS_FAILURE,
  ];

  const topics = [
    KAFKA_TOPIC.TTS_TIMEOUT,
    KAFKA_TOPIC.UPDATE_PROGRESS_REQUEST,
    KAFKA_TOPIC.PACKAGE_ACTIVATED,
    KAFKA_TOPIC.CHARACTERS_PROCESSING,
    KAFKA_TOPIC.SECONDS_PROCESSING,
    KAFKA_TOPIC.STT_SECONDS_PROCESSING,
    KAFKA_TOPIC.MIGRATE_ACCOUNT,
    KAFKA_TOPIC.OVERWRITE_ACCOUNT,
    KAFKA_TOPIC.SWITCH_VERSION,
    KAFKA_TOPIC.SYNC_DICTIONARY,
    KAFKA_TOPIC.BLOCK_USER,
    KAFKA_TOPIC.GET_LINK_SRT_FILE_SUCCESS,
    KAFKA_TOPIC.GET_LINK_SRT_FILE_FAILURE,
    KAFKA_TOPIC.MIGRATE_DUBBING_TO_STUDIO,
  ];
  if (!SYNTHESIS_BY_GATEWAY) topics.push(...synthesisTopics);

  topics.forEach((topic) => consumer.subscribe({ topic }));

  await consumer.run({
    eachMessage: async ({ topic, partition, message }) => {
      try {
        logger.info(
          {
            topic,
            partition,
            offset: message.offset,
            timestamp: message.timestamp,
            key: message.key ? message.key.toString() : null,
            value: message.value.toString(),
          },
          { ctx: 'KafkaConsume' },
        );

        calculateMetricBasedOnKafkaTopic(topic);

        const data = camelCaseKeys(JSON.parse(message.value.toString()), {
          deep: true,
        });

        switch (topic) {
          case KAFKA_TOPIC.SENTENCE_TOKENIZATION_SUCCESS: {
            const {
              requestId,
              index,
              voice,
              sentences,
              sentencesUrl,
              ttsCoreVersion,
              duration,
            } = data;
            handleSentenceTokenizationSuccessResponse({
              requestId,
              index,
              sentences,
              sentencesUrl,
              voiceCode: voice.code,
              ttsCoreVersion,
              duration,
            }).catch((error) => {
              logger.error(error, { ctx: 'HandleSentenceTokenizationSuccess' });
            });
            break;
          }

          case KAFKA_TOPIC.SENTENCE_TOKENIZATION_FAILURE: {
            const { requestId, errorCode, errorMessage } = data;
            handleSentenceTokenizationFailureResponse({
              requestId,
              errorCode,
              error: errorMessage,
            }).catch((error) => {
              logger.error(error, { ctx: 'HandleSentenceTokenizationFailure' });
            });
            break;
          }

          case KAFKA_TOPIC.SYNTHESIS_SUCCESS: {
            const {
              requestId,
              index,
              subIndex,
              ttsId,
              text,
              audioLink,
              audioName,
              phrases,
              t2ADuration: t2aDuration,
              synthesisDuration,
              cache,
            } = data;

            handleSynthesisSuccessResponse({
              requestId,
              index,
              text,
              subIndex,
              ttsId,
              audioLink,
              audioName,
              t2aDuration,
              synthesisDuration,
              phrases,
              cache,
            }).catch((error) => {
              logger.error(error, { ctx: 'HandleSynthesisSuccess' });
            });
            break;
          }

          case KAFKA_TOPIC.SYNTHESIS_FAILURE: {
            const {
              requestId,
              index,
              subIndex,
              ttsId,
              errorCode,
              errorMessage,
            } = data;
            handleSynthesisFailureResponse({
              requestId,
              index,
              ttsId,
              subIndex,
              error: errorMessage,
              errorCode,
            }).catch((error) => {
              logger.error(error, { ctx: 'HandleSynthesisFailure' });
            });
            break;
          }
          case KAFKA_TOPIC.CACHING_SYNTHESIS_SUCCESS: {
            const {
              requestId,
              statusCode,
              audioLink,
              totalTime,
              message: cachingMessage,
              getSynthesisRequestAt,
              startInvokeLambdaFunctionAt,
              endInvokeLambdaFunctionAt,
            } = data;

            handleCachingSynthesisSuccess({
              requestId,
              statusCode,
              audioLink,
              totalTime,
              cachingMessage,
              getSynthesisRequestAt,
              startInvokeLambdaFunctionAt,
              endInvokeLambdaFunctionAt,
            }).catch((error) => {
              logger.error(error, { ctx: 'HandleCachingSynthesisSuccess' });
            });
            break;
          }

          case KAFKA_TOPIC.CACHING_SYNTHESIS_FAILURE: {
            const {
              requestId,
              statusCode,
              message: cachingMessage,
              totalTime: cachingTime,
            } = data;

            handleCachingSynthesisFailure({
              requestId,
              statusCode,
              cachingMessage,
              cachingTime,
            }).catch((error) => {
              logger.error(error, { ctx: 'HandleCachingSynthesisFailure' });
            });
            break;
          }

          case KAFKA_TOPIC.JOIN_AUDIOS_SUCCESS: {
            const { requestId, audioLink, duration } = data;
            handleJoinAudiosSuccessResponse({
              requestId,
              audioLink,
              duration,
            }).catch((error) => {
              logger.error(error, { ctx: 'HandleJoinAudiosSuccess' });
            });
            break;
          }

          case KAFKA_TOPIC.JOIN_AUDIOS_FAILURE: {
            const { requestId, errorCode, errorMessage } = data;
            handleJoinAudiosFailureResponse({
              requestId,
              error: errorMessage,
              errorCode,
            }).catch((error) => {
              logger.error(error, { ctx: 'HandleJoinAudiosFailure' });
            });
            break;
          }

          case KAFKA_TOPIC.TTS_TIMEOUT: {
            const { requestId } = data;
            handleRequestTimeOut(requestId).catch((error) => {
              logger.error(error, { ctx: 'HandleRequestTimeout' });
            });
            break;
          }

          case KAFKA_TOPIC.UPDATE_PROGRESS_REQUEST: {
            const { requestId, status, progress, audioLink, error, errorCode } =
              data;
            handleUpdateProgressRequest({
              requestId,
              status,
              progress,
              audioLink,
              error,
              errorCode,
            }).catch((err) => {
              logger.error(err, { ctx: 'HandleUpdateProgressRequest' });
            });
            break;
          }

          case KAFKA_TOPIC.PACKAGE_ACTIVATED: {
            updateByNewPackage(data).catch((error) => {
              logger.error(error, { ctx: 'UpdateByNewPackage' });
            });
            break;
          }

          case KAFKA_TOPIC.CHARACTERS_PROCESSING: {
            updateByCharacters(data).catch((error) => {
              logger.error(error, { ctx: 'UpdateByCharacters' });
            });
            break;
          }

          case KAFKA_TOPIC.SECONDS_PROCESSING: {
            await updateBySeconds(data).catch((error) => {
              logger.error(error, { ctx: 'UpdateBySeconds' });
            });
            break;
          }

          case KAFKA_TOPIC.STT_SECONDS_PROCESSING: {
            await updateSttSeconds(data).catch((error) => {
              logger.error(error, { ctx: 'UpdateSttSeconds' });
            });
            break;
          }

          case KAFKA_TOPIC.MIGRATE_ACCOUNT: {
            migrateAccount(data).catch((error) => {
              logger.error(error, { ctx: 'MigrateAccount' });
            });
            break;
          }

          case KAFKA_TOPIC.OVERWRITE_ACCOUNT: {
            migrateAccount({ overwrite: true, ...data }).catch((error) => {
              logger.error(error, { ctx: 'OverwriteAccount' });
            });
            break;
          }

          case KAFKA_TOPIC.SWITCH_VERSION: {
            const { dictionary, userId, event } = data;
            if (event === SWITCH_VERSION_EVENT.UPGRADE && dictionary?.length)
              syncDictionary(userId, dictionary).catch((error) => {
                logger.error(error, { ctx: 'SwitchVersion' });
              });
            break;
          }

          case KAFKA_TOPIC.BLOCK_USER: {
            const { blockUserId, isBlock } = data;
            updateUserById(blockUserId, { isBlock }).catch((error) => {
              logger.error(error, { ctx: 'BlockUser' });
            });
            break;
          }

          case KAFKA_TOPIC.SYNC_DICTIONARY: {
            const { dictionary, userId } = data;
            if (dictionary?.length) {
              syncDictionary(userId, dictionary).catch((error) => {
                logger.error(error, { ctx: 'SyncDictionary' });
              });
            }
            break;
          }

          case KAFKA_TOPIC.GET_LINK_SRT_FILE_SUCCESS: {
            const {
              requestId,
              userId,
              email,
              phoneNumber,
              seconds,
              voice,
              subtitleLink,
            } = data;
            handleGetLinkSrtFileSuccess({
              requestId,
              subtitleLink,
              email,
              phoneNumber,
              userId,
              seconds,
              voice,
            }).catch((error) => {
              logger.error(error, { ctx: 'HandleGetLinkSrtFileSuccess' });
            });
            break;
          }

          case KAFKA_TOPIC.GET_LINK_SRT_FILE_FAILURE: {
            const { requestId, errorMessage } = data;
            logger.error(errorMessage, { ctx: 'HandleGetLinkSrtFileFailure' });
            handleGetLinkSrtFileFail({
              requestId,
              errorMessage,
            }).catch((error) => {
              logger.error(error, { ctx: 'HandleGetLinkSrtFileFailure' });
            });
            break;
          }

          case KAFKA_TOPIC.MIGRATE_DUBBING_TO_STUDIO: {
            handleMigrateDubbingToStudio(data).catch((error) => {
              logger.error(error, { ctx: 'HandleMigrateDubbingToStudio' });
            });
            break;
          }

          default:
            break;
        }
      } catch (error) {
        logger.error(error, { ctx: 'KafkaConsume' });
      }
    },
  });
};

const publishConsume = async () => {
  await publishConsumer.connect();

  const topics = [
    KAFKA_TOPIC.AI_VOICE_PUBLISH_TTS_SUCCESS,
    KAFKA_TOPIC.AI_VOICE_PUBLISH_TTS_FAILURE,
  ];
  topics.forEach((topic) => publishConsumer.subscribe({ topic }));

  await publishConsumer.run({
    eachMessage: async ({ topic, partition, message }) => {
      try {
        logger.info(
          {
            topic,
            partition,
            offset: message.offset,
            timestamp: message.timestamp,
            key: message.key ? message.key.toString() : null,
            value: message.value.toString(),
          },
          { ctx: 'KafkaConsume' },
        );

        const data = camelCaseKeys(JSON.parse(message.value.toString()), {
          deep: true,
        });

        switch (topic) {
          case KAFKA_TOPIC.AI_VOICE_PUBLISH_TTS_SUCCESS: {
            const { requestId, demo, audioLink } = data;

            if (demo)
              handleTtsDemoResponse({
                requestId,
                status: REQUEST_STATUS.SUCCESS,
                audioLink,
              });

            const res = global.REQUEST_DIRECT[requestId];
            if (!res) return;
            sendApiResponse({ request: data, res }).catch((error) => {
              logger.error(error, { ctx: 'AIVoicePublishTTSSuccess' });
            });
            break;
          }

          case KAFKA_TOPIC.AI_VOICE_PUBLISH_TTS_FAILURE: {
            const { requestId, demo, errorCode, errorMessage } = data;

            if (demo)
              handleTtsDemoResponse({
                requestId,
                status: REQUEST_STATUS.FAILURE,
              });

            const res = global.REQUEST_DIRECT[requestId];
            if (!res) return;
            sendApiResponse({
              request: data,
              res,
              errorMessage,
              errorCode,
            }).catch((error) => {
              logger.error(error, { ctx: 'AIVoicePublishTTSFailure' });
            });
            break;
          }

          default:
            break;
        }
      } catch (error) {
        logger.error(error, { ctx: 'KafkaConsume' });
      }
    },
  });
};

consume().catch((e) => {
  logger.error(e, { ctx: 'KafkaConsume' });
});
publishConsume().catch((e) => {
  logger.error(e, { ctx: 'KafkaConsume' });
});

const errorTypes = ['unhandledRejection', 'uncaughtException'];
const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2'];

errorTypes.map((type) => {
  process.on(type, async (e) => {
    try {
      logger.error(e, { ctx: 'ProcessError', type });
      await consumer.disconnect();
      await publishConsumer.disconnect();
      process.exit(0);
    } catch (_) {
      process.exit(1);
    }
  });
});

signalTraps.map((type) => {
  process.once(type, async () => {
    try {
      await consumer.disconnect();
      await publishConsumer.disconnect();
    } finally {
      process.kill(process.pid, type);
    }
  });
});

const moment = require('moment');
const {
  AI_VOICE_APP_ID,
  AI_VOICE_CALLBACK_URL,
  SYNTHESIS_BY_GATEWAY,
} = require('../configs');
const {
  WS_MESSAGE_TYPE,
  KAFKA_TOPIC,
  LOADING_SYNTHESIS_FACTOR,
  REQUEST_TYPE,
  RESPONSE_TYPE,
  REQUEST_STATUS,
  REDIS_KEY_PREFIX,
  REDIS_KEY_TTL,
  SME_PACKAGE_CODES,
  PACKAGE_FEATURE,
  BLOCK_SYNTHESIS_STATUS,
} = require('../constants');
const { deleteInProgressRequest } = require('../daos/inProgressRequest');
const {
  updateRequestByIdInRedis,
  findRequestByIdInRedis,
  updateFinalRequestFromRedisToDB,
  findRequestById,
  updateRequestById,
} = require('../daos/request');
const { updateBlockAudioLink } = require('../daos/project');
const {
  getTotalTtsByRequestId,
  getTotalSuccessTtsByRequestId,
  checkCompletedIndexInRedis,
  migrateTtsFromRedisToDB,
  countRealTtsInRedis,
} = require('../daos/tts');
const code = require('../errors/code');

const { sendApiResponse } = require('./apiResponse');
const { refundCharacters } = require('./characterProcessing');
const { sendMessage } = require('./kafka/producer');
const {
  processQueueWhenRequestSuccess,
  processQueueWhenRequestFailure,
} = require('./queue');
const Caching = require('./caching');
// const { decreaseMaxPreviewByRequest } = require('./user');
const { findUser } = require('../daos/user');
const { findDictionary } = require('../daos/dictionary');
const { findClientPause } = require('../daos/clientPause');
const {
  getProcessingTime,
  saveStepProcessingTime,
  saveProcessingTime,
  getCurrentStep,
  getStartTimeFromStep,
} = require('./processingTime');
const {
  decrTotalInProgressRequests,
  decrTotalProcessingRequests,
} = require('./metrics');
const logger = require('../utils/logger');
const { isEmptyObject } = require('../utils/object');
const { refundSeconds } = require('./secondProcessing');
const { getFeatureValue } = require('./growthbook');
const { FEATURE_KEYS } = require('../constants/featureKeys');
const { getPackageUsageOptions } = require('./package');
const { getSynthesisComputePlatform } = require('./synthesisComputePlatform');
const { getVoiceCloningVoice } = require('./voiceCloning');
const { TtsGateAdapter } = require('../adapters/ttsgate');

const handleTtsDemoResponse = ({ requestId, status, audioLink }) => {
  const ws = REQUESTS[requestId];
  if (!ws) return;

  const message = {
    type: WS_MESSAGE_TYPE.SYNTHESIS,
    status: 1,
    result: { requestId, status, audioLink },
  };
  require('./ws').sendMessage(ws, message);

  // if (status === REQUEST_STATUS.SUCCESS) decreaseMaxPreviewByRequest(requestId);
};

const handleStreamAudio = async ({
  requestId,
  index,
  subIndex,
  audioLink,
  phrases,
  tts,
}) => {
  const ws = REQUESTS[requestId];
  if (!ws) return;

  const request = await findRequestByIdInRedis(requestId);
  if (!SYNTHESIS_BY_GATEWAY) {
    tts = request.ttsIds?.reduce(
      (prevTts, ttsItem) => {
        // eslint-disable-next-line no-shadow
        const { index, subIndex } = ttsItem;
        const ttsIndexOrder = prevTts.find((order) => order.index === index);
        if (ttsIndexOrder) {
          if (ttsIndexOrder.lastSubIndex < subIndex)
            ttsIndexOrder.lastSubIndex = subIndex;
        } else prevTts.push({ index, lastSubIndex: subIndex });

        return prevTts;
      },
      [{ index: 0, lastSubIndex: 0 }],
    );
  }

  logger.info(
    { requestId, index, subIndex, audioLink, audioIndexOrder: tts, phrases },
    { ctx: 'StreamAudio' },
  );

  require('./ws').sendMessage(ws, {
    type: WS_MESSAGE_TYPE.STREAM_REQUEST,
    status: 1,
    result: {
      status: REQUEST_STATUS.SUCCESS,
      requestId,
      index,
      subIndex,
      audioLink,
      tts,
      phrases,
    },
  });

  if (index === 0 && subIndex === 0) {
    const firstStreamAudioAt = moment();
    const firstStreamAudioDuration = moment
      .duration(firstStreamAudioAt.diff(request.createdAt))
      .asSeconds();
    await updateRequestByIdInRedis(requestId, { firstStreamAudioDuration });
    logger.info('handleStreamAudio', {
      ctx: 'StreamAudio',
      firstStreamAudioDuration,
    });
  }
};

const refundResources = ({ userId, appId, requestId, seconds }) => {
  if (seconds) refundSeconds({ userId, appId, requestId });
  else refundCharacters({ userId, appId, requestId });
};

const handleRefundResources = ({
  requestId,
  type,
  userId,
  packageCode,
  appId,
  seconds,
}) => {
  const DUBBING_PACKAGE_PREFIX = 'DUBBING';
  const isProcessBySecond = packageCode.includes(DUBBING_PACKAGE_PREFIX);

  if (type === REQUEST_TYPE.DUBBING && isProcessBySecond)
    refundResources({ requestId, userId, appId, seconds });
  else refundResources({ requestId, userId, appId });
};

const handleTtsFailure = ({ request, errorCode, errorMessage }) => {
  decrTotalInProgressRequests();
  decrTotalProcessingRequests();

  const {
    _id: requestId,
    userId,
    app: appId,
    usedCharacters = {},
    type,
    responseType,
    demo,
    packageCode,
    seconds,
    projectId,
  } = request;

  // Refund and send to notification
  if (!demo) {
    processQueueWhenRequestFailure({
      userId,
      requestId,
      requestType: type,
    });
    if (!SME_PACKAGE_CODES.includes(packageCode))
      handleRefundResources({
        requestId,
        type,
        packageCode,
        userId,
        appId,
        seconds,
        usedCharacters,
      });
    sendMessage(KAFKA_TOPIC.TTS_FAILURE, {
      value: {
        projectId,
        requestId,
        appId,
        userId,
        paidCharacters: usedCharacters.paid || 0,
        bonusCharacters: usedCharacters.bonus || 0,
      },
    });
  }

  const { title, text, sentences, paragraphs, ...requestData } = request;

  // Send callback for sync request api
  if (type === REQUEST_TYPE.API && responseType === RESPONSE_TYPE.INDIRECT) {
    sendApiResponse({
      request: { requestId, ...requestData },
      errorCode,
      errorMessage,
    }).catch((e) => {
      logger.error(e, { ctx: 'SendApiResponseTtsFailure', requestId });
    });
  }

  // Publish to kafka for demo and async request
  const hasPublishRequest =
    demo ||
    (type === REQUEST_TYPE.API && responseType === RESPONSE_TYPE.DIRECT);
  if (hasPublishRequest)
    sendMessage(KAFKA_TOPIC.AI_VOICE_PUBLISH_TTS_FAILURE, {
      value: { errorMessage, errorCode, requestId, ...requestData },
    });

  if (request?.projectId) {
    if (type === REQUEST_TYPE.STUDIO) {
      // No using await here since we only need to save it without using its result
      // and avoid blocking response time
      updateBlockAudioLink({
        projectId: request.projectId,
        requestId: request._id,
        audioLink: null,
        status: BLOCK_SYNTHESIS_STATUS.FAILURE,
      }).catch((err) => {
        logger.error(err, {
          ctx: 'HandleJoinAudiosFailureResponse',
          requestId,
          projectId: request.projectId,
        });
      });
      return;
    }
    // Do not use await here to avoid blocking the response time
    // Use catch to avoid breaking the main process
    // ADVISE: avoid inline require()
    require('./dubbing')
      .updateProjectStatusFromRequest({
        projectId: request.projectId,
        requestStatus: REQUEST_STATUS.FAILURE,
        requestId,
        userId,
      })
      .catch((err) => {
        logger.error(err, { ctx: 'UpdateProjectStatusFromRequest', requestId });
      });
  }
};

const handleTtsSuccess = (request, preSendResponseAt) => {
  decrTotalInProgressRequests();
  decrTotalProcessingRequests();

  const {
    _id: requestId,
    userId,
    demo,
    type,
    responseType,
    audioLink,
    endedAt,
    processingAt,
    projectId,
  } = request;

  // Decrease number of pending and inprogress requests and Send to notification
  if (!demo) {
    processQueueWhenRequestSuccess({
      userId,
      requestId,
      requestType: type,
    });

    sendMessage(KAFKA_TOPIC.TTS_SUCCESS, {
      value: {
        requestId,
        projectId,
        userId,
        audioLink,
        preSendResponseAt,
        endedAt,
        processingAt,
      },
    });
  }

  const { title, text, sentences, paragraphs, ...requestData } = request;

  // Send callback for sync request api
  if (
    (type === REQUEST_TYPE.API || type === REQUEST_TYPE.API_CACHING) &&
    responseType === RESPONSE_TYPE.INDIRECT
  ) {
    sendApiResponse({
      request: { requestId, text, sentences, ...requestData },
    }).catch((e) => {
      logger.error(e, { ctx: 'SendApiResponseTtsSuccess', requestId });
    });
  }

  // Publish to kafka for demo and async request
  const hasPublishRequest =
    demo ||
    ((type === REQUEST_TYPE.API || type === REQUEST_TYPE.API_CACHING) &&
      responseType === RESPONSE_TYPE.DIRECT);
  if (hasPublishRequest)
    sendMessage(KAFKA_TOPIC.AI_VOICE_PUBLISH_TTS_SUCCESS, {
      value: { requestId, text, sentences, preSendResponseAt, ...requestData },
    });

  if (request?.projectId) {
    if (type === REQUEST_TYPE.STUDIO) {
      // No using await here since we only need to save it without using its result
      // and avoid blocking response time
      const audioExpiredAt = moment(request.endedAt).add(
        request.retentionPeriod,
        'days',
      );

      updateBlockAudioLink({
        projectId: request.projectId,
        requestId: request._id,
        audioLink,
        status: BLOCK_SYNTHESIS_STATUS.SUCCESS,
        audioExpiredAt,
      }).catch((err) => {
        logger.error(err, {
          ctx: 'HandleJoinAudiosSuccessResponse',
          requestId,
          projectId: request.projectId,
          audioLink,
        });
      });

      return;
    }
    // Do not use await here to avoid blocking the response time
    // Use catch to avoid breaking the main process
    require('./dubbing')
      .updateProjectStatusFromRequest({
        projectId: request.projectId,
        requestStatus: REQUEST_STATUS.SUCCESS,
        requestId,
        userId,
      })
      .catch((err) => {
        logger.error(err, { ctx: 'UpdateProjectStatusFromRequest', requestId });
      });
  }
};

const handleDubbingFailure = ({ request, errorCode, errorMessage }) => {
  const { _id: requestId, userId, app: appId } = request;

  sendMessage(KAFKA_TOPIC.DUBBING_FAILURE, {
    value: {
      requestId,
      appId,
      userId,
      seconds: 0,
      errorCode,
      errorMessage,
    },
  });
};

const checkCompletedSynthesis = async ({
  requestId,
  numberOfSentences,
  numOfCompletedTts,
  numOfTts,
}) => {
  logger.info(
    { requestId, numOfTts, numOfCompletedTts },
    { ctx: 'CheckCompletedSynthesis' },
  );
  if (numOfTts < numberOfSentences) return false;
  if (numOfCompletedTts < numOfTts) return false;

  const indexes = Array.from(Array(numberOfSentences).keys());
  const checks = await Promise.all(
    indexes.map(async (index) => {
      const check = await checkCompletedIndexInRedis(requestId, index);
      return check;
    }),
  );
  return checks.every((check) => check);
};

/** publish event TTS_IN_PROGRESS  */
const handleUpdateProgressTTS = async ({
  requestId,
  userId,
  progress,
  processingAt,
}) => {
  const message = { value: { requestId, userId, progress, processingAt } };
  sendMessage(KAFKA_TOPIC.TTS_IN_PROGRESS, message);
};

const updateProgressWhenSynthesisSuccess = async (requestId, userId) => {
  const totalTtsRequest = await getTotalTtsByRequestId(requestId);
  const totalSuccessTtsRequest = await getTotalSuccessTtsByRequestId(requestId);

  const progress =
    LOADING_SYNTHESIS_FACTOR.SENTENCE_TOKENIZATION * 100 +
    Math.round(
      (totalSuccessTtsRequest / (totalTtsRequest || 1)) *
        LOADING_SYNTHESIS_FACTOR.SYNTHESIS_SENTENCE_SUCCESS *
        100,
    );

  handleUpdateProgressTTS({ requestId, userId, progress });
};

const calProgressWhenSynthesisSuccess = (numOfCompletedTts, numOfTts) => {
  const { SENTENCE_TOKENIZATION, SYNTHESIS_SENTENCE_SUCCESS } =
    LOADING_SYNTHESIS_FACTOR;
  const maxSynthesisProgress =
    SENTENCE_TOKENIZATION + SYNTHESIS_SENTENCE_SUCCESS;
  const normalizedNumOfTts = numOfTts || 1;

  const progress =
    SENTENCE_TOKENIZATION * 100 +
    Math.round(
      (numOfCompletedTts / normalizedNumOfTts) *
        SYNTHESIS_SENTENCE_SUCCESS *
        100,
    );

  return progress / 100 > maxSynthesisProgress
    ? maxSynthesisProgress * 100
    : progress;
};

const updateProgressWhenSynthesisSuccessInRedis = async ({
  requestId,
  userId,
  progress,
}) => {
  handleUpdateProgressTTS({ requestId, userId, progress });
};

const getProgress = async (requestId) => {
  const completedTtsKey = `${REDIS_KEY_PREFIX.COMPLETED_SYNTHESIS}_${requestId}`;
  const numOfCompletedTts = await Caching.RedisRepo.get(completedTtsKey);
  const numOfTts = await countRealTtsInRedis(requestId);

  const progress = calProgressWhenSynthesisSuccess(numOfCompletedTts, numOfTts);
  return progress;
};

const processRequestTimeOutFromCache = async ({ requestId, endedAt }) => {
  const finalRequest = await updateRequestByIdInRedis(requestId, {
    status: REQUEST_STATUS.FAILURE,
    error: 'Request time out',
    endedAt,
    errorCode: code.TIMEOUT,
  });

  updateFinalRequestFromRedisToDB(requestId, finalRequest);
  migrateTtsFromRedisToDB(requestId);

  return finalRequest;
};

const processRequestTimeOutFromDB = async ({ requestId, endedAt }) => {
  const requestInDB = await findRequestById(requestId);
  let finalRequest;
  if (requestInDB?.status === REQUEST_STATUS.IN_PROGRESS) {
    finalRequest = await updateRequestById(requestId, {
      status: REQUEST_STATUS.FAILURE,
      error: 'Request time out',
      endedAt,
      errorCode: code.TIMEOUT,
    });
  }
  return finalRequest;
};

const getFinalTimeOutRequest = async (requestId) => {
  const requestInRedis = await findRequestByIdInRedis(requestId);
  const endedAt = new Date();

  // If requestInRedis is an empty object, it means that the request is not in Cache
  const isInprogressRequestInCache =
    !isEmptyObject(requestInRedis) &&
    requestInRedis.status === REQUEST_STATUS.IN_PROGRESS;

  const finalRequest = isInprogressRequestInCache
    ? await processRequestTimeOutFromCache({ requestId, endedAt })
    : await processRequestTimeOutFromDB({ requestId, endedAt });

  return finalRequest;
};

const handleRequestTimeOut = async (requestId) => {
  const finalRequest = await getFinalTimeOutRequest(requestId);
  if (!finalRequest) return;

  const processingTime = await getProcessingTime(requestId);
  const currentStep = getCurrentStep(processingTime);
  const { startTime } = processingTime;

  const startCurrentStepAt = getStartTimeFromStep(processingTime, currentStep);

  await deleteInProgressRequest(requestId);

  await saveStepProcessingTime({
    requestId,
    step: currentStep,
    startTime: startCurrentStepAt,
  });

  saveProcessingTime({
    requestId,
    startTime,
    status: REQUEST_STATUS.FAILURE,
  });
  handleTtsFailure({
    request: finalRequest,
    errorCode: code.REQUEST_TIMEOUT,
  });
};

const handleUpdateProgressRequest = async ({
  requestId,
  status,
  progress,
  audioLink,
  error,
  errorCode,
}) => {
  const request = await findRequestByIdInRedis(requestId);
  const { userId } = request;

  switch (status) {
    case REQUEST_STATUS.IN_PROGRESS: {
      await updateProgressWhenSynthesisSuccessInRedis({
        requestId,
        userId,
        progress,
      });
      break;
    }

    case REQUEST_STATUS.SUCCESS: {
      require('./synthesis')
        .handleJoinAudiosSuccessResponse({
          requestId,
          audioLink,
        })
        .catch((err) => {
          logger.error({ err }, { ctx: 'HandleUpdateProgressRequest' });
        });
      break;
    }

    case REQUEST_STATUS.FAILURE: {
      // ADVISE: avoid using inline require()
      require('./synthesis').handleJoinAudiosFailureResponse({
        requestId,
        error,
        errorCode,
      });
      break;
    }

    default:
      break;
  }
};

// First check if the voice is in the VOICES array, then check if the voice is in the voice cloning collection
const getTTSGateVoiceCode = async (voiceCode) => {
  const voice = VOICES.find((v) => v.code === voiceCode);
  if (voice) return voice?.ttsGateCode || voiceCode;

  const voiceInfo = await getVoiceCloningVoice(voiceCode);
  return voiceInfo?.ttsGateCode || voiceCode;
};

const saveTtsIdsInRequest = async (requestId) => {
  const request = await findRequestByIdInRedis(requestId);

  const numberOfIndexSentences = request.numberOfIndexSentences || 1;
  const numOfSentenceCompletedKey = `${REDIS_KEY_PREFIX.COMPLETED_SENTENCE_TOKENIZATION}_${requestId}`;
  const numOfSentenceCompleted = await Caching.GlobalCounter.increase(
    numOfSentenceCompletedKey,
  );
  await Caching.RedisRepo.expire(
    numOfSentenceCompletedKey,
    REDIS_KEY_TTL.COMPLETED_SENTENCE_TOKENIZATION,
  );

  logger.info(
    { numberOfIndexSentences, numOfSentenceCompleted },
    { ctx: 'RequestRedis' },
  );
  if (Number(numOfSentenceCompleted) === numberOfIndexSentences) {
    const { sentenceKeys = [] } = request;
    const sentencesCache = await Caching.RedisRepo.multiGet(sentenceKeys);

    const checkNullElement = sentencesCache.some((sentence) => !sentence);
    if (!checkNullElement) {
      const ttsIds = sentencesCache.reduce((acc, sentence) => {
        return [...acc, ...JSON.parse(sentence)];
      }, []);
      request.ttsIds = ttsIds;
      const requestKey = `${REDIS_KEY_PREFIX.REQUEST}_${requestId}`;
      const requestKeyTtl = REDIS_KEY_TTL.SYNTHESIS_REQUEST;

      await Caching.RedisRepo.set(
        requestKey,
        JSON.stringify(request),
        requestKeyTtl,
      );

      const totalTtsRequestKey = `${REDIS_KEY_PREFIX.TOTAL_TTS}_${requestId}`;
      const totalTtsRequest = ttsIds.length;
      await Caching.RedisRepo.set(
        totalTtsRequestKey,
        totalTtsRequest,
        REDIS_KEY_TTL.SENTENCE,
      );
    }
  }
};

// TODO: Pass params request so that we can use instead of query in db
const callApiSynthesis = async (requestId) => {
  try {
    const request = await findRequestById(requestId);
    const {
      text,
      title,
      sentences,
      audioType,
      bitrate,
      sampleRate,
      speed: speedRate,
      fromVn,
      backgroundMusic,
      retentionPeriod,
      userId,
      type,
      subtitleLink,
      demo,
      processingAt,
      sentencesVoiceCode,
      returnTimestampWords,
      synthesisComputePlatform,
      isPriority,
      isRealTime,
    } = request;
    let voice = request.voice || {};
    const user = await findUser({ _id: userId });
    const studioUsageOptions = await getPackageUsageOptions({
      userId,
      packageCode: user.packageCode,
      userUsageOptions: user,
    });
    const hasVoiceCloningFeatures = studioUsageOptions?.features?.includes(
      PACKAGE_FEATURE.AI_VOICE_CLONING,
    );
    const hasInstantVoiceCloningFeatures =
      studioUsageOptions?.features?.includes(
        PACKAGE_FEATURE.AI_INSTANT_VOICE_CLONING,
      );

    // ADVISE: a config value based on user, should be in User's role
    const synthesisVoiceCode =
      getFeatureValue(FEATURE_KEYS.VOICE_CLONING, { userId }) ||
      hasVoiceCloningFeatures ||
      hasInstantVoiceCloningFeatures;
    const hasVoiceInRequest = Object.keys(voice).length > 0;

    // The findRequestById function won't return the voice if voice cloning is used,
    // because it only looks up the voice in the voice collection.
    // Therefore, if voice cloning is enabled, we need to retrieve the voice from Redis.
    if (synthesisVoiceCode && !hasVoiceInRequest) {
      const requestCached = await findRequestByIdInRedis(requestId);
      voice = requestCached.voice;
    }

    // ADVISE: wasted business? we already have dictionary and clientPause
    const dictionary = await findDictionary(request.userId);
    const clientPause = await findClientPause(request.userId);
    const { paragraphBreak, sentenceBreak, majorBreak, mediumBreak } =
      clientPause || {};

    // Update progress
    const progress = LOADING_SYNTHESIS_FACTOR.START_PROCESSING * 100;
    updateRequestByIdInRedis(requestId, { progress });
    await handleUpdateProgressTTS({
      requestId,
      userId,
      progress,
      processingAt,
    });

    // #region format data before sending to tts-api (gateway)

    const newSentences = !text
      ? await Promise.all(
          sentences.map(
            async ({
              elements,
              characters,
              text: textSentence,
              speed: speedSentence,
              voiceCode,
              isMonetizable,
              credits,
              ...rest
            } = {}) => ({
              inputText: textSentence,
              speedRate: speedSentence,
              voiceCode: await getTTSGateVoiceCode(voiceCode),
              synthesisComputePlatform: getSynthesisComputePlatform({
                voiceCode,
                userId,
                type,
                demo,
              }),
              ...rest,
            }),
          ),
        )
      : [];

    const { link, volume } = backgroundMusic || {};
    const ttsGateVoiceCode = await getTTSGateVoiceCode(voice?.code);
    const commonPayload = {
      title,
      appId: AI_VOICE_APP_ID,
      callbackUrl: `${AI_VOICE_CALLBACK_URL}/api/v1/tts/callback-tts-gate`,
      callbackUpdateProgressUrl: demo
        ? `${AI_VOICE_CALLBACK_URL}/api/v1/tts/update-request-progress`
        : null,
      responseType: RESPONSE_TYPE.INDIRECT,
      audioType,
      bitrate: bitrate % 1000 === 0 ? bitrate / 1000 : bitrate,
      sampleRate: sampleRate ? parseInt(sampleRate, 10) : null,
      fromVn,
      voiceCode: ttsGateVoiceCode,
      retentionPeriod,
      clientUserId: userId,
      sessionId: requestId,
      speedRate,
      synthesisCcr: user?.concurrentRequest,
    };

    const ttsPayload = {
      ...commonPayload,

      inputText: text,
      sentences: text ? null : newSentences,
      backgroundMusic: { link, volume },
      dictionary: dictionary?.words || null,
      // ADVISE: user can use clientPause or not, should prepare in the request beforehand
      clientPause: studioUsageOptions?.features?.includes(
        PACKAGE_FEATURE.CLIENT_PAUSE,
      )
        ? { paragraphBreak, sentenceBreak, majorBreak, mediumBreak }
        : {},
      returnTimestamp: returnTimestampWords,
      isPriority: isPriority || undefined,
      isRealTime: isRealTime || undefined,
      synthesisComputePlatform,
    };

    const dubbingPayload = {
      ...commonPayload,

      subtitleLink,
      sentencesVoiceCode,
      synthesisComputePlatform,
    };

    // #endregion

    // ADVISE: we can check for type === REQUEST_TYPE.DUBBING earlier, then process payload separately. split the func smaller
    const apiEndpoint = type === REQUEST_TYPE.DUBBING ? 'dubbing' : 'tts';

    const res = await TtsGateAdapter.executeWithRetry({
      apiEndpoint,
      data: type === REQUEST_TYPE.DUBBING ? dubbingPayload : ttsPayload,
      requestId,
    });

    if (res.status !== 1) {
      // ADVISE: avoid using inline require(). The inline require here because "synthesis" also call "ttsprocessing"
      require('./synthesis').handleJoinAudiosFailureResponse({
        requestId,
        error: res.errorMessage,
        errorCode: res.errorCode,
      });
      throw new Error(res.message);
    }
  } catch (error) {
    logger.error(error, { cxt: 'CallApiSynthesis', requestId });

    const errorMessage = JSON.stringify(error?.response?.data) || error.message;
    // ADVISE: avoid using inline require(). The inline require here because "synthesis" also call "ttsprocessing"
    require('./synthesis').handleJoinAudiosFailureResponse({
      requestId,
      error: errorMessage,
      errorCode: error.code,
    });
  }
};

// ADVISE: should move to Dictionary Service
/** return PronunciationDict-words exist in text */
const findUsedWordsInPronunciationDict = (text, pronunciationDict) =>
  pronunciationDict.filter((item) => text.includes(item.word));

module.exports = {
  handleTtsDemoResponse,
  handleStreamAudio,
  handleTtsFailure,
  handleTtsSuccess,
  checkCompletedSynthesis,
  calProgressWhenSynthesisSuccess,
  handleUpdateProgressTTS,
  updateProgressWhenSynthesisSuccess,
  updateProgressWhenSynthesisSuccessInRedis,
  handleRequestTimeOut,
  handleUpdateProgressRequest,
  saveTtsIdsInRequest,
  callApiSynthesis,
  getProgress,
  handleDubbingFailure,
  findUsedWordsInPronunciationDict,
};
